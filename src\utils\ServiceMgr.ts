/**
 * 客服管理器
 * 负责接入第三方 AIHelp 客服系统，管理初始化、登录、消息数、UI 展示等
 */
import { showLoadingToast, closeToast } from "vant";
import { useGlobalStore } from "@/stores/global";

export enum ServiceType {
  Custom = "E004CUSTOM",
  Vip = "E003vip",
  Login = "E004CUSTOM",
  Game = "E005game",
  Test = "test123",
  Test001 = "E001",
  Test003 = "E003",
}

declare global {
  interface Window {
    AIHelpSupport?: any;
  }
}

export class serviceMgr {
  private domain = "nustar.aihelp.net";
  private appid = "nustar_platform_7f60038c5d234a165a07c1dc62a91722";
  private language = "English";
  private static isInited = false;
  private static isLogined = false;
  private static isOpened = false;

  static _instance: serviceMgr;

  static get instance() {
    if (this._instance) {
      return this._instance;
    }
    this._instance = new serviceMgr();
    return this._instance;
  }

  /**
   * 初始化客服 SDK
   */
  public init(stype?: ServiceType) {
    if (serviceMgr.isInited) return;
    // showLoadingToast({
    //   duration: 0,
    //   forbidClick: true,
    //   message: "loading",
    // });
    // SDK 未加载，重试
    if (!window.AIHelpSupport) {
      setTimeout(() => this.init(stype), 1000);
      return;
    }

    // 初始化参数
    const config = {
      domain: this.domain,
      appId: this.appid,
      language: this.language,
    };

    // 注册初始化回调
    window.AIHelpSupport.registerAsyncEventListener("initialization", (jsonData: any) => {
      serviceMgr.isInited = true;
      // closeToast();
      this.updateUserInfo();
      if (stype) this.openChat(stype);
    });

    // 注册消息到达回调
    window.AIHelpSupport.registerAsyncEventListener("messageArrival", (jsonEventData: any) => {
      // 这里可以更新全局未读消息数 ,触发前端事件刷新红点
      const globalStore = useGlobalStore();
      globalStore.hasUnreadCustomerMsg = Number(jsonEventData?.msgCount) > 0 ? true : false;
    });
    // 注册窗口打开,可加背景遮罩
    window.AIHelpSupport.registerAsyncEventListener("sessionOpen", () => {
      serviceMgr.isOpened = true;
      this.createMask();
      // closeToast();
    });
    // 注册窗口打开,移除背景遮罩
    window.AIHelpSupport.registerAsyncEventListener("sessionClose", () => {
      serviceMgr.isOpened = false;
      this.destroyMask();
      this.getUnreadMsgCount();
    });

    // 注册登录回调
    window.AIHelpSupport.registerAsyncEventListener("userLogin", () => {
      console.log("login success");
      serviceMgr.isLogined = true;
      this.getUnreadMsgCount();
      this.pollUnreadMsgCount();
      window.AIHelpSupport.resetUserInfo();
    });

    // 初始化 SDK
    window.AIHelpSupport.initialize(config);
  }

  /**
   * 退出客服
   */
  public logout() {
    if (!serviceMgr.isLogined) return;
    window.AIHelpSupport.logout();
  }

  /**
   * 更新用户信息
   */
  public updateUserInfo() {
    const globalStore = useGlobalStore();
    const user = globalStore.userInfo;
    if (!user && !user.user_id) return;
    const isVip = Number(user.is_vip) === 1 ? "yes" : "no";
    const userConfig = {
      userName: user.nickname || "",
      userTags: `vip = ${isVip}`,
      customData: JSON.stringify({
        tags: `vip = ${isVip}`,
        username: user.nickname || "i am nick",
      }),
    };
    window.AIHelpSupport.updateUserInfo(userConfig);
    setTimeout(() => window.AIHelpSupport.resetUserInfo(), 1000);
  }

  //用户登陆
  public login() {
    if (serviceMgr.isLogined) return;
    if (!serviceMgr.isInited) {
      let self = this;
      setTimeout(() => {
        self.login();
      }, 2000);
      return;
    }
    const globalStore = useGlobalStore();
    const user = globalStore.userInfo;
    if (!user && !user.user_id) return;
    const isVip = Number(user.is_vip) === 1 ? "yes" : "no";
    const userConfig = {
      userName: user.nickname || "",
      userTags: `vip = ${isVip}`,
      customData: JSON.stringify({
        tags: `vip = ${isVip}`,
        username: user.nickname || "i am nick",
      }),
    };
    let loginConfig = {
      userId: user?.user_id || "",
      userConfig: userConfig,
      isEnterpriseAuth: false,
    };
    window["AIHelpSupport"].login(loginConfig);
  }

  /**
   * 打开客服聊天窗口
   */
  public openChat(stype: ServiceType) {
    this.init(stype);
    const apiConfig = {
      entranceId: stype,
      welcomeMessage: "WELCOME!",
    };
    if (!serviceMgr.isOpened) {
      window.AIHelpSupport.show(apiConfig);
    }
  }

  /**
   * 获取未读消息数
   */
  public getUnreadMsgCount() {
    window.AIHelpSupport.fetchUnreadMessageCount();
  }

  /**
   * 每5分钟轮询未读消息数
   */
  private pollUnreadMsgCount() {
    this.getUnreadMsgCount();
    setTimeout(() => this.pollUnreadMsgCount(), 5 * 60 * 1000);
  }

  /**
   * 关闭客服窗口
   */
  public closeChat() {
    if (serviceMgr.isOpened) {
      window.AIHelpSupport.close();
    }
  }

  private getMaskElement() {
    return document.getElementById("AIHelpSupportBoxMask");
  }
  // 创建背景弹窗
  private createMask() {
    const AIHelpSupportBoxMaskElement = this.getMaskElement();
    if (AIHelpSupportBoxMaskElement) {
      AIHelpSupportBoxMaskElement.style.display = "block";
    } else {
      const maskElement = document.createElement("div");
      maskElement.id = "AIHelpSupportBoxMask";
      maskElement.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 99;
      touch-action:none;
      pointer-events: auto;
    }
  `;
      // 全面阻止所有事件传播
      const stopEvent = (e: Event) => {
        e.stopPropagation();
        e.preventDefault();
      };

      // 阻止所有相关事件
      maskElement.addEventListener("click", stopEvent);
      maskElement.addEventListener("touchstart", stopEvent);
      maskElement.addEventListener("touchmove", stopEvent);
      maskElement.addEventListener("touchend", stopEvent);
      maskElement.addEventListener("mousedown", stopEvent);
      maskElement.addEventListener("mouseup", stopEvent);
      maskElement.addEventListener("mousemove", stopEvent);
      document.body.appendChild(maskElement);
    }
    document.body.style.overflow = "hidden";
  }
  // 隐藏背景弹窗
  private destroyMask() {
    const AIHelpSupportBoxMaskElement = this.getMaskElement();
    if (AIHelpSupportBoxMaskElement) {
      AIHelpSupportBoxMaskElement.style.display = "none";
    }
    document.body.style.overflow = "";
  }
}

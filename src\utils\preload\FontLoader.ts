/**
 * 字体配置接口
 */
interface FontConfig {
  family: string;
  weight: string;
  style: string;
  url: string;
  priority: "high" | "medium" | "low";
  format?: string;
  display?: "auto" | "block" | "swap" | "fallback" | "optional";
  fallback?: boolean;
}

/**
 * 字体预加载工具类
 */
export class FontLoader {
  private static instance: FontLoader;
  private loadedFonts: Set<string> = new Set();
  private loadingPromises: Map<string, Promise<void>> = new Map();

  static getInstance(): FontLoader {
    if (!FontLoader.instance) {
      FontLoader.instance = new FontLoader();
    }
    return FontLoader.instance;
  }

  /**
   * 字体配置
   */
  private fontConfigs: FontConfig[] = [
    // iconfont 图标字体 - 高优先级
    // D-DIN 字体系列 - 使用 public 目录路径，构建后可直接访问
    {
      family: "D-DIN",
      weight: "400",
      style: "normal",
      url: "/fonts/D-DIN/D-DIN.ttf",
      priority: "high" as const,
    },
    // {
    //   family: "D-DIN",
    //   weight: "700",
    //   style: "normal",
    //   url: "/fonts/D-DIN/D-DIN-Bold.ttf",
    //   priority: "high" as const,
    // },
    // {
    //   family: "DINPro",
    //   weight: "300",
    //   style: "normal",
    //   url: "/fonts/D-DIN/DINPro-Light.ttf",
    //   priority: "medium" as const,
    // },
    // {
    //   family: "DINPro",
    //   weight: "500",
    //   style: "normal",
    //   url: "/fonts/D-DIN/DINPro-Medium.ttf",
    //   priority: "medium" as const,
    // },
    // Inter 字体系列 - 常用字重，使用 public 目录路径
    {
      family: "Inter",
      weight: "400",
      style: "normal",
      url: "/fonts/Inter/Inter-Regular-9.otf",
      priority: "high" as const,
    },
    // {
    //   family: "Inter",
    //   weight: "500",
    //   style: "normal",
    //   url: "/fonts/Inter/Inter-Medium-8.otf",
    //   priority: "high" as const,
    // },
    // {
    //   family: "Inter",
    //   weight: "600",
    //   style: "normal",
    //   url: "/fonts/Inter/Inter-SemiBold-10.otf",
    //   priority: "medium" as const,
    // },
    // {
    //   family: "Inter",
    //   weight: "700",
    //   style: "normal",
    //   url: "/fonts/Inter/Inter-Bold-4.otf",
    //   priority: "medium" as const,
    // },
    // // 其他字重 - 低优先级
    // {
    //   family: "Inter",
    //   weight: "300",
    //   style: "normal",
    //   url: "/fonts/Inter/Inter-Light-7.otf",
    //   priority: "low" as const,
    // },
    // {
    //   family: "Inter",
    //   weight: "800",
    //   style: "normal",
    //   url: "/fonts/Inter/Inter-ExtraBold-5.otf",
    //   priority: "low" as const,
    // },
  ];

  /**
   * 预加载单个字体
   */
  private async loadFont(config: FontConfig): Promise<void> {
    const fontKey = `${config.family}-${config.weight}-${config.style}-${
      config.format || "default"
    }`;

    if (this.loadedFonts.has(fontKey)) {
      return Promise.resolve();
    }

    if (this.loadingPromises.has(fontKey)) {
      return this.loadingPromises.get(fontKey)!;
    }

    const loadPromise = new Promise<void>((resolve, reject) => {
      // 对于 iconfont，使用更优化的加载方式
      if (config.family === "iconfont") {
        this.loadIconFont(config, resolve, reject);
      } else {
        this.loadRegularFont(config, resolve, reject);
      }
    });

    this.loadingPromises.set(fontKey, loadPromise);
    return loadPromise;
  }

  /**
   * 加载图标字体
   */
  private loadIconFont(
    config: FontConfig,
    resolve: () => void,
    reject: (error: any) => void
  ): void {
    const fontFace = new FontFace(config.family, `url(${config.url})`, {
      weight: config.weight,
      style: config.style,
      display: config.display || "block", // 图标字体使用 block 显示
    });

    const fontKey = `${config.family}-${config.weight}-${config.style}-${
      config.format || "default"
    }`;

    fontFace
      .load()
      .then((loadedFace) => {
        (document.fonts as any).add(loadedFace);
        this.loadedFonts.add(fontKey);
        console.log(`图标字体加载成功: ${config.family} (${config.format || "unknown"})`);
        resolve();
      })
      .catch((error) => {
        console.warn(`图标字体加载失败: ${config.family} (${config.format})`, error);
        // 图标字体加载失败时不阻塞应用
        resolve();
      })
      .finally(() => {
        this.loadingPromises.delete(fontKey);
      });
  }

  /**
   * 加载常规字体
   */
  private loadRegularFont(
    config: FontConfig,
    resolve: () => void,
    reject: (error: any) => void
  ): void {
    const fontFace = new FontFace(config.family, `url(${config.url})`, {
      weight: config.weight,
      style: config.style,
      display: config.display || "swap",
    });

    const fontKey = `${config.family}-${config.weight}-${config.style}-${
      config.format || "default"
    }`;

    fontFace
      .load()
      .then((loadedFace) => {
        (document.fonts as any).add(loadedFace);
        this.loadedFonts.add(fontKey);
        resolve();
      })
      .catch((error) => {
        reject(error);
      })
      .finally(() => {
        this.loadingPromises.delete(fontKey);
      });
  }

  /**
   * 预加载高优先级字体
   */
  async preloadHighPriorityFonts(): Promise<void> {
    const highPriorityFonts = this.fontConfigs.filter((config) => config.priority === "high");

    try {
      await Promise.all(highPriorityFonts.map((config) => this.loadFont(config)));
    } catch (error) {
      // 静默处理错误
    }
  }

  /**
   * 预加载中优先级字体
   */
  async preloadMediumPriorityFonts(): Promise<void> {
    const mediumPriorityFonts = this.fontConfigs.filter((config) => config.priority === "medium");

    try {
      await Promise.all(mediumPriorityFonts.map((config) => this.loadFont(config)));
    } catch (error) {
      // 静默处理错误
    }
  }

  /**
   * 预加载低优先级字体
   */
  async preloadLowPriorityFonts(): Promise<void> {
    const lowPriorityFonts = this.fontConfigs.filter((config) => config.priority === "low");

    try {
      await Promise.all(lowPriorityFonts.map((config) => this.loadFont(config)));
    } catch (error) {
      // 静默处理错误
    }
  }

  /**
   * 预加载所有字体
   */
  async preloadAllFonts(): Promise<void> {
    try {
      // 按优先级顺序加载
      await this.preloadHighPriorityFonts();
      await this.preloadMediumPriorityFonts();
      await this.preloadLowPriorityFonts();
    } catch (error) {
      // 静默处理错误
    }
  }

  /**
   * 检查字体是否已加载
   */
  isFontLoaded(family: string, weight: string = "400", style: string = "normal"): boolean {
    const fontKey = `${family}-${weight}-${style}`;
    return this.loadedFonts.has(fontKey);
  }

  /**
   * 获取已加载的字体列表
   */
  getLoadedFonts(): string[] {
    return Array.from(this.loadedFonts);
  }

  /**
   * 在浏览器空闲时加载字体
   * 使用 requestIdleCallback 在空闲时加载非关键字体
   */
  loadFontsOnIdle(): void {
    // 检查浏览器是否支持 requestIdleCallback
    if ("requestIdleCallback" in window) {
      this.scheduleIdleFontLoading();
    } else {
      // 降级方案：使用 setTimeout 延迟加载
      this.scheduleFallbackFontLoading();
    }
  }

  /**
   * 使用 requestIdleCallback 调度字体加载
   */
  private scheduleIdleFontLoading(): void {
    const idleCallback = (deadline: IdleDeadline) => {
      // console.log("浏览器空闲，开始加载字体...");

      // 按优先级分批加载字体
      this.loadFontsInBatches(deadline);
    };

    // 设置超时时间，确保字体最终会被加载
    const options = {
      timeout: 10000, // 10秒超时
    };

    (window as any).requestIdleCallback(idleCallback, options);
  }

  /**
   * 分批加载字体 (在空闲时间内)
   */
  private async loadFontsInBatches(deadline: IdleDeadline): Promise<void> {
    // 获取未加载的字体，按优先级排序
    const unloadedFonts = this.getUnloadedFontsByPriority();

    for (const font of unloadedFonts) {
      // 检查是否还有空闲时间 (至少保留5ms)
      if (deadline.timeRemaining() > 5) {
        try {
          await this.loadFont(font);
          // console.log(`空闲时加载字体完成: ${font.family} (${font.format || "default"})`);
        } catch (error) {
          // console.warn(`空闲时加载字体失败: ${font.family}`, error);
        }
      } else {
        // 时间不够，调度下一次空闲时继续
        // console.log("空闲时间不足，调度下次继续加载字体");
        this.scheduleIdleFontLoading();
        break;
      }
    }
  }

  /**
   * 获取未加载的字体，按优先级排序
   */
  private getUnloadedFontsByPriority(): FontConfig[] {
    const priorityOrder = { high: 1, medium: 2, low: 3 };

    return this.fontConfigs
      .filter((config) => {
        const fontKey = `${config.family}-${config.weight}-${config.style}-${
          config.format || "default"
        }`;
        return !this.loadedFonts.has(fontKey) && !this.loadingPromises.has(fontKey);
      })
      .sort((a, b) => priorityOrder[a.priority] - priorityOrder[b.priority]);
  }

  /**
   * 降级方案：使用 setTimeout 延迟加载
   */
  private scheduleFallbackFontLoading(): void {
    console.log("使用降级方案延迟加载字体");

    // 延迟2秒开始加载，避免影响关键渲染
    setTimeout(() => {
      this.loadRemainingFonts();
    }, 2000);
  }

  /**
   * 加载剩余的字体 (降级方案)
   */
  private async loadRemainingFonts(): Promise<void> {
    const unloadedFonts = this.getUnloadedFontsByPriority();

    // 分批加载，每批间隔100ms，避免阻塞
    for (let i = 0; i < unloadedFonts.length; i++) {
      const font = unloadedFonts[i];

      try {
        await this.loadFont(font);
        console.log(`延迟加载字体完成: ${font.family} (${font.format || "default"})`);

        // 每加载一个字体后稍作停顿
        if (i < unloadedFonts.length - 1) {
          await new Promise((resolve) => setTimeout(resolve, 100));
        }
      } catch (error) {
        console.warn(`延迟加载字体失败: ${font.family}`, error);
      }
    }
  }
}

// 导出单例实例
export const fontLoader = FontLoader.getInstance();

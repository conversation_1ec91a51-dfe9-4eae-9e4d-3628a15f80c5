<template>
  <div class="showcase-container">
    <h1>ZAccountCard 组件展示</h1>
    
    <!-- ZBaseAccountCard 基础用法 -->
    <section class="showcase-section">
      <h2>1. ZBaseAccountCard 基础用法</h2>
      <div class="card-grid">
        <!-- 水平布局 -->
        <div class="card-demo">
          <h3>水平布局 (horizontal)</h3>
          <ZBaseAccountCard
            :item="{ name: 'GCash', icon: 'gcash-icon' }"
            :card-width="300"
            :card-height="60"
            content-layout="horizontal"
            :show-right-area="true"
          >
            <template #right>
              <div class="demo-icon">✓</div>
            </template>
          </ZBaseAccountCard>
        </div>

        <!-- 垂直布局 -->
        <div class="card-demo">
          <h3>垂直布局 (vertical)</h3>
          <ZBaseAccountCard
            :item="{ name: 'Maya' }"
            :card-width="200"
            :card-height="120"
            content-layout="vertical"
            :show-bg-icon="true"
            bg-icon-name="maya"
            :show-right-area="true"
          >
            <template #right>
              <div class="demo-icon">⚙️</div>
            </template>
          </ZBaseAccountCard>
        </div>

        <!-- 垂直内联布局 -->
        <div class="card-demo">
          <h3>垂直内联布局 (vertical-inline)</h3>
          <ZBaseAccountCard
            :item="{ name: 'Bank Transfer' }"
            :card-width="250"
            :card-height="100"
            content-layout="vertical-inline"
            :show-bg-icon="true"
            bg-icon-name="bank"
            :show-bottom-area="true"
          >
            <template #content="{ item }">
              <div class="inline-content">
                <div class="icon-placeholder">🏦</div>
                <span>{{ item.name }}</span>
              </div>
            </template>
            <template #bottom>
              <div class="demo-footer">**** **** **** 1234</div>
            </template>
          </ZBaseAccountCard>
        </div>
      </div>
    </section>

    <!-- ZWithdrawAccountCard 用法 -->
    <section class="showcase-section">
      <h2>2. ZWithdrawAccountCard 实际用法</h2>
      <div class="card-grid">
        <!-- VIEW 状态 -->
        <div class="card-demo">
          <h3>VIEW 状态</h3>
          <ZWithdrawAccountCard
            :item="gcashItem"
            status="view"
            :card-width="280"
            :card-height="134"
          />
        </div>

        <!-- EDIT 状态 -->
        <div class="card-demo">
          <h3>EDIT 状态</h3>
          <ZWithdrawAccountCard
            :item="mayaItem"
            status="edit"
            :card-width="280"
            :card-height="134"
            @click="handleCardClick"
          />
        </div>

        <!-- CHECK 状态 -->
        <div class="card-demo">
          <h3>CHECK 状态</h3>
          <ZWithdrawAccountCard
            :item="gcashItem"
            status="check"
            :card-width="280"
            :card-height="134"
            :checked-account-id="gcashItem.account_id"
            @click="handleCardClick"
          />
        </div>
      </div>
    </section>

    <!-- 充值卡片用法 -->
    <section class="showcase-section">
      <h2>3. 充值卡片用法 (index.vue)</h2>
      <div class="card-grid">
        <div class="card-demo">
          <h3>充值账户选择</h3>
          <ZAccountCard
            :item="depositItem"
            :card-width="200"
            :card-height="56"
          />
        </div>
      </div>
    </section>

    <!-- 自定义主题展示 -->
    <section class="showcase-section">
      <h2>4. 不同主题展示</h2>
      <div class="card-grid">
        <div class="card-demo" v-for="theme in themes" :key="theme.name">
          <h3>{{ theme.name }}</h3>
          <ZBaseAccountCard
            :item="{ name: theme.name }"
            :card-width="200"
            :card-height="80"
            content-layout="horizontal"
            :show-bg-icon="true"
            :bg-icon-name="theme.name.toLowerCase()"
          />
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ZBaseAccountCard from '../ZBaseAccountCard.vue';
import ZWithdrawAccountCard from '../ZWithdrawAccountCard.vue';
import ZAccountCard from '../index.vue';

// 测试数据
const gcashItem = ref({
  account_id: "gcash-123",
  account_no: "****************",
  type: 12, // GCash
  name: "GCash"
});

const mayaItem = ref({
  account_id: "maya-456",
  account_no: "****************",
  type: 11, // Maya
  name: "Maya"
});

const depositItem = ref({
  name: "GCash",
  icon: "gcash-icon",
  account_type: 12
});

const themes = ref([
  { name: 'GCash' },
  { name: 'Maya' },
  { name: 'Bank' },
  { name: 'Palawan' },
  { name: 'QRPH' }
]);

const handleCardClick = (item: any) => {
  console.log('Card clicked:', item);
  alert(`Clicked: ${item.name || item.account_id}`);
};
</script>

<style scoped lang="scss">
.showcase-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
  font-family: Arial, sans-serif;
}

.showcase-section {
  background: white;
  margin-bottom: 30px;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.card-demo {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  
  h3 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 14px;
    font-weight: 600;
  }
  
  display: flex;
  flex-direction: column;
  align-items: center;
}

h1 {
  color: #212529;
  margin: 0 0 30px 0;
  text-align: center;
  font-size: 28px;
}

h2 {
  color: #343a40;
  margin: 0 0 15px 0;
  font-size: 20px;
  border-bottom: 2px solid #007bff;
  padding-bottom: 8px;
}

.demo-icon {
  font-size: 20px;
  color: white;
}

.inline-content {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-weight: 600;
  
  .icon-placeholder {
    font-size: 24px;
  }
}

.demo-footer {
  color: white;
  font-family: "D-DIN", monospace;
  font-size: 18px;
  font-weight: 700;
}
</style>

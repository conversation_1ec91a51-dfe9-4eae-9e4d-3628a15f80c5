{"name": "nustar-h5", "version": "********", "private": true, "type": "module", "scripts": {"start": "npm run start:test", "start:test": "vite --mode test", "start:pre": "vite --mode pre", "start:prod": "vite --mode production", "build:dev": "vite build --mode test", "build:test": "vite build --mode test", "build:pre": "vite build --mode pre", "build:prod": "vite build --mode production", "analyze": "npm run build:prod && node scripts/analyze-bundle.js"}, "dependencies": {"@vant/auto-import-resolver": "^1.3.0", "@vant/touch-emulator": "^1.4.0", "@vue/runtime-dom": "^3.5.16", "@vueuse/core": "^12.0.0", "axios": "^1.7.9", "child_process": "^1.0.2", "compression-webpack-plugin": "^11.1.0", "countup.js": "2.9.0", "decimal.js": "^10.5.0", "echarts": "^5.6.0", "hls.js": "^1.6.7", "numeral": "^2.0.6", "pinia": "^3.0.2", "qs": "^6.14.0", "vant": "^4.8.1", "vconsole": "^3.15.1", "vite-plugin-pwa": "^1.0.2", "vite-svg-loader": "^5.1.0", "vue": "^3.5.13", "vue-lazyload": "^3.0.0", "vue-router": "^4.4.5", "workbox-window": "^7.3.0"}, "devDependencies": {"@types/lodash-es": "^4.17.12", "@types/node": "^22.9.3", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "flv.js": "^1.6.2", "lodash-es": "^4.17.21", "pinia-plugin-persistedstate": "^4.3.0", "postcss-mobile-forever": "^5.0.0", "postcss-px-to-viewport": "^1.1.1", "sass": "^1.83.0", "typescript": "~5.6.3", "unplugin-auto-import": "^19.2.0", "unplugin-vue-components": "^28.5.0", "vite": "^6.0.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-mock": "^2.9.8", "vite-plugin-style-import": "1.4.1", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^2.1.10"}}
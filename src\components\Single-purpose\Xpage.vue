<template>
  <div
    class="xpage-container"
    :style="{ backgroundColor }"
    @scroll.passive="handleScroll"
    ref="container"
  >
    <transition name="fade">
      <div
        class="xpage-nav"
        :style="{
          background: showNav ? navBarStyle.backgroundColor || '#fff' : 'transparent',
          color: showNav ? navBarStyle.color || '#000' : '#000',
        }"
      >
        <span class="xpage-nav-back" @click="onBack">
          <slot name="left-icon" :showNav="showNav" :showNavBack="showNavBack">
            <ZIcon
              type="icon-fanhui1"
              v-show="showNavBack"
              :color="showNav ? navBarStyle.color || '#000' : '#fff'"
              :size="22"
              :style="showNav ? { backgroundColor: 'transparent' } : {}"
            />
          </slot>
        </span>
        <span
          class="xpage-nav-title"
          :style="{
            fontSize: navBarStyle.fontSize || '20px',
            color: showNav ? navBarStyle.color || '#000' : '#fff',
          }"
        >
          <slot name="title">
            {{ showNav || initShowNav ? navTitle || $route.meta.title : "" }}
          </slot>
        </span>
        <span
          class="xpage-nav-right"
          :style="{ color: showNav ? navBarStyle.color || '#000' : '#fff' }"
        >
          <slot name="right"></slot>
        </span>
      </div>
    </transition>
    <div class="xpage-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <ZLoading />
      </div>
      <slot v-else />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineEmits } from "vue";
import { useRouter, useRoute } from "vue-router";
import type { CSSProperties } from "vue";

interface Props {
  navTitle?: string;
  showNavBack?: boolean;
  navBarStyle?: CSSProperties;
  navShowThreshold?: number;
  backgroundColor?: string;
  initShowNav?: boolean;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  navTitle: "",
  showNavBack: true,
  navBarStyle: () => ({}),
  navShowThreshold: 50,
  backgroundColor: "#fff",
  initShowNav: true,
  loading: false,
});

const emit = defineEmits<{
  back: [event: CustomEvent];
}>();
const router = useRouter();
const route = useRoute();

const showNav = ref(false);
const container = ref<HTMLElement | null>(null);

const handleScroll = () => {
  if (!container.value) return;
  showNav.value = container.value.scrollTop > props.navShowThreshold;
};

const onBack = (event?: Event) => {
  // 创建一个自定义事件对象来检查是否被阻止
  const backEvent = new CustomEvent("back", { cancelable: true });
  emit("back", backEvent);

  // 如果父组件没有阻止默认行为，则自动返回
  if (!backEvent.defaultPrevented) {
    router.back();
  }
};
</script>

<style scoped lang="scss">
.xpage-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow-y: auto;
  background: #fff;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  position: relative;
  z-index: 10;
  // iOS Safari 兼容性
  -webkit-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
  // 确保在 iOS 上正确显示
  min-height: 200px;
}

.xpage-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 48px;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  z-index: 10;
  transition: all 0.2s;
  padding: 0 12px;
  background: transparent;
  gap: 10px;
}

.xpage-nav-back {
  display: flex;
  align-items: center;
}
.icon-fanhui1 {
  border-radius: 50%;
  width: 32px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.3);
}

.xpage-nav-title {
  flex: 1;
  font-size: 24px;
  font-family: "Inter";
  font-weight: 600;
  text-align: center;
  line-height: 32px;
  max-width: 263px;
  margin: 0 auto;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  // color: #fff;
}

.xpage-nav-right {
  // width: 40px;
  text-align: center;
  width: 32px;
  // color: #fff;
}

.xpage-content {
  padding-top: 0;
  min-height: 100vh;
  padding-bottom: constant(safe-area-inset-bottom); ///兼容 IOS<11.2/
  padding-bottom: env(safe-area-inset-bottom); ///兼容 IOS>11.2/
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>

import { defineStore } from "pinia";
import { getKycStatus, getKycBonus, getKycData } from "@/api/kyc";
import { useGlobalStore } from "@/stores/global";
import { useAutoPopMgrStore } from "@/stores/autoPopMgr";
import { showToast } from "vant";
import { getGlobalDialog } from "@/enter/vant";
import { CHANEL_TYPE } from "@/utils/config/GlobalConstant";
import router from "@/router";

// KYC 验证场景
export enum KYC_SCENE {
  UNKNOWN = -1, // 未知
  Login = 0, //登录场景
  Withdraw, //提现 验证
  MyCenter, //个人中心
  GoThirdGame, //打开第三方游戏
  Deposit, //充值
}

// KYC 认证状态
export enum KYC_STATUS {
  NO_VERIFY = 0, // 未验证
  COMPLETE = 1, // 已认证
  REVIEWING = 2, // 审核中
  REJECTED = 3, // 已拒绝
  UNKNOWN = -1, // 未知状态
  Hundred = 100, //100是指：FULL版本，且填写过表单1的场景
}

// KYC 认证类型
export enum KYC_TYPE {
  CLOSE = 0, // 关闭状态
  FULL = 1, // 完整版
  SIMPLE = 2, // 简版
  STANDING = 3, // 听证会版
  UNKNOWN = -1, // 未知状态
}

// KYC 奖励信息接口
export interface KYC_BONUS_INFO {
  is_kyc_completed: number; // 是否完成 KYC (1: 已完成, 0: 未完成)
  kyc_completed_reward: string; // KYC 完成奖励金额
  status: number; // 显示状态 (1: 显示, 0: 不显示)
}

// 初始状态值
const initValue = {
  kycStatus: KYC_STATUS.UNKNOWN,
  kycType: KYC_TYPE.UNKNOWN,
  rejectMsg: "You are younger than 21.", //拒绝信息
  kycVerifyCallback: undefined as ((state: boolean) => void) | undefined,
  //进入验证的 入口状态
  inSceneType: KYC_SCENE.UNKNOWN,
  isInRegisterPeriod: true, //账号注册是否三天内 默认在
  // 手机号绑定
  showVerifyPreconditionsDialog: false,
  kycBonusInfo: {
    is_kyc_completed: 0, // 默认未完成
    kyc_completed_reward: "0", // 默认无奖励
    status: 0, // 默认不显示
  } as KYC_BONUS_INFO,
  kycData: {},
  showKycLoginDalog: false, //登录首页，简版 KYC弹窗默认不弹出
  poping: false, //是否有弹窗 正在显示用于自动弹窗功能
};

export const useKycMgrStore = defineStore("kycMgr", {
  state: () => ({
    ...initValue,
  }),

  getters: {
    CHANEL_TYPE() {
      const globalStore = useGlobalStore();
      return globalStore.channel;
    },
    // 是否显示 KYC奖励 提示
    showKycCompletedRewardTip: (state) => {
      return (
        state.kycBonusInfo.status === 1 &&
        state.kycBonusInfo.is_kyc_completed === 0 &&
        Number(state.kycBonusInfo.kyc_completed_reward) > 0
      );
    },

    // 是否已完成 KYC
    isKycCompleted: (state) => {
      return state.kycStatus === KYC_STATUS.COMPLETE;
    },

    // 是否正在审核中
    isKycReviewing: (state) => {
      return state.kycStatus === KYC_STATUS.REVIEWING;
    },

    // 是否被拒绝
    isKycRejected: (state) => {
      return state.kycStatus === KYC_STATUS.REJECTED;
    },

    // 简版
    isSimpleKyc: (state) => {
      return state.kycType === KYC_TYPE.SIMPLE;
    },
    // 详版
    isFullKyc: (state) => {
      return state.kycType === KYC_TYPE.FULL;
    },
    // 听证会版
    isStandingKyc: (state) => {
      return state.kycType === KYC_TYPE.STANDING;
    },
  },

  actions: {
    // 私有方法：获取 globalStore 实例
    _getGlobalStore() {
      return useGlobalStore();
    },

    // 私有方法：检查用户是否已登录
    _isLoggedIn(): boolean {
      return !!this._getGlobalStore().token;
    },
    // 初始化请求
    async fetchKycInit() {
      if (!this._isLoggedIn()) return;
      try {
        await Promise.all([this.fetchKycState(), this.fetchKycData(), this.fetchKycBonus()]);
      } catch (error) {}
    },
    // 获取 KYC 状态
    async fetchKycState(successCallback?: (kycStateData?: any) => void, failCallback?: () => void) {
      if (!this._isLoggedIn()) return;
      try {
        const res = await getKycStatus({});
        this.kycStatus = Number(res.status);
        this.kycType = Number(res.kyc_status);
        this.rejectMsg = res.reject || this.rejectMsg;
        this.isInRegisterPeriod = res.is_in_register_period;
        successCallback && successCallback(res);
      } catch (error) {
        failCallback && failCallback(error);
      }
    },
    // 获取kyc数据
    async fetchKycData() {
      if (!this._isLoggedIn()) return;
      try {
        const res = await getKycData({});
        this.kycData = res;
      } catch (error) {}
    },

    // 获取 KYC 认证奖励信息
    async fetchKycBonus() {
      if (!this._isLoggedIn()) return;
      try {
        const res = await getKycBonus({});
        this.kycBonusInfo = {
          is_kyc_completed: Number(res.is_kyc_completed),
          kyc_completed_reward: String(res.kyc_completed_reward),
          status: Number(res.status),
        };
      } catch (error) {
        console.error("获取 KYC 奖励信息失败:", error);
      }
    },
    //
    //验证审核中：处理函数
    handleReviewScene() {
      switch (this.inSceneType) {
        case KYC_SCENE.Login:
        case KYC_SCENE.Deposit:
        case KYC_SCENE.GoThirdGame:
          if (this.kycType === KYC_TYPE.FULL && !this.isInRegisterPeriod) {
            //完整版 非注册期
            this.reviewPop();
            this.backToTarget(false);
            return;
          } else {
            //继续游戏不弹窗
            this.backToTarget(true);
          }
          break;
        case KYC_SCENE.MyCenter:
          //继续下一步不弹窗这里是查询输入信息
          break;
        case KYC_SCENE.Withdraw:
          //提现 只在完整版 弹窗提示！
          this.backToTarget();
          this.reviewPop();
          break;
        default:
          break;
      }
    },

    // 获取 KYC 状态
    async fetchKycStatusCallBack() {
      this.fetchKycState(
        () => {
          if (this.kycStatus === KYC_STATUS.COMPLETE) {
            this.backToTarget(true);
          } else if (this.kycStatus === KYC_STATUS.REVIEWING) {
            this.handleReviewScene();
          } else {
            this.backToTarget(false);
          }
          //如果是首次登录 不要先弹出 走autopopmgr
          if (this.kycType !== KYC_TYPE.CLOSE) {
            //未完成kyc 提示弹窗
            if (this.kycStatus == KYC_STATUS.NO_VERIFY || this.kycStatus == KYC_STATUS.Hundred) {
              this.noVerifyPop();
            } else if (this.kycStatus == KYC_STATUS.REJECTED) {
              // 被拒绝 提示弹窗
              this.rejectPop();
            }
          }
        },
        () => {
          this.backToTarget(false);
        }
      );
    },

    // 刷新 KYC 状态
    async refreshKycStatus() {
      this.fetchKycState(() => {
        //已拒绝
        if (this.kycStatus === KYC_STATUS.REJECTED) {
          this.rejectPop();
        } else if (this.kycStatus === KYC_STATUS.REVIEWING) {
          //审核中
          this.reviewPop();
        } else if (this.kycStatus === KYC_STATUS.COMPLETE) {
          //审核完成
          this.poping = false;
          showToast("KYC Verification Certification Passed");
        }
      });
    },

    // 重置 KYC 状态
    resetKycState() {
      this.kycStatus = KYC_STATUS.UNKNOWN;
      this.kycType = KYC_TYPE.UNKNOWN;
      this.kycBonusInfo = { ...initValue.kycBonusInfo };
      this.kycData = {};
      this.kycVerifyCallback = undefined;
      this.poping = false;
    },

    // 更新 KYC 状态（用于其他地方手动更新）
    updateKycState(state: KYC_STATUS, simple?: KYC_TYPE) {
      this.kycStatus = state;
      if (simple !== undefined) {
        this.kycType = simple;
      }
    },

    backToTarget(state = false) {
      this.kycVerifyCallback?.(state);
      this.kycVerifyCallback = undefined;
    },
    /**
     * 入口方法：校验 KYC
     */
    async verifyKyc(inType: KYC_SCENE, callback?: (isVerify: boolean) => void) {
      this.kycVerifyCallback = callback;
      this.inSceneType = inType;

      if (!this._isLoggedIn()) {
        this.backToTarget(false);
        return;
      }
      if (this.CHANEL_TYPE !== CHANEL_TYPE.WEB) {
        this.backToTarget(true);
        return;
      }
      // 关闭KYC 验证
      if (this.kycType === KYC_TYPE.CLOSE) {
        this.backToTarget(true);
        return;
      }

      //每次进来都刷新一下状态 这样能保持 最新状态更新
      if (this.kycStatus !== KYC_STATUS.UNKNOWN) {
        // await this.fetchKycState();
        this.fetchKycState();
      }

      if (this.kycStatus === KYC_STATUS.UNKNOWN) {
        await this.fetchKycStatusCallBack();
      } else if (this.kycStatus === KYC_STATUS.NO_VERIFY) {
        //未验证
        this.noVerifyPop();
        if (this.kycType == KYC_TYPE.SIMPLE) {
          if (this.inSceneType == KYC_SCENE.Withdraw) {
            this.backToTarget(false);
          } else {
            this.backToTarget(true);
          }
        }
      } else if (this.kycStatus === KYC_STATUS.REJECTED) {
        //已拒绝
        this.rejectPop();
        if (this.kycType == KYC_TYPE.SIMPLE) {
          if (this.inSceneType != KYC_SCENE.Withdraw) {
            this.backToTarget(true);
          } else {
            this.backToTarget(false);
          }
        }
      } else if (this.kycStatus === KYC_STATUS.REVIEWING) {
        this.handleReviewScene();
      } else if (this.kycStatus === KYC_STATUS.Hundred) {
        //未验证和拒绝状态 验证下一步
        if (this.isInRegisterPeriod) {
          this.backToTarget(true);
        } else {
          this.noVerifyPop();
        }
      } else {
        //进入第三方游戏和提现的时候用到
        this.backToTarget(true);
      }
    },

    /**
     * 未提交
     */
    noVerifyPop() {
      // 简版、关闭状态的 KYC弹窗， 只在提现和登录时显示
      if (this.kycType == KYC_TYPE.SIMPLE || this.kycType == KYC_TYPE.CLOSE) {
        if (![KYC_SCENE.Withdraw, KYC_SCENE.Login].includes(this.inSceneType)) {
          return;
        }
      }
      //简版 KYC弹窗， 在登录时可以通过配置控制是否显示
      if (
        !this.showKycLoginDalog &&
        this.inSceneType == KYC_SCENE.Login &&
        this.kycType == KYC_TYPE.SIMPLE
      ) {
        return;
      }

      this.showKycLoginDalog = false;
      this.poping = true;

      const dialogFn = () => {
        const $dialog = getGlobalDialog();
        $dialog({
          title: "KYC Verification",
          message: `<div style="color:#222;text-align:center;">Your account is not yet fully verified</div>`,
          describe: `<div style="color:#999;text-align:center;">Your access to a certain service on the NUSTAR Online will be restricted.</div>`,
          confirmText: "Verify Now",
          onConfirm: async () => {
            //验证是否 绑定手机号
            this.checkPreconditions();
          },
          onCancel: () => {
            if (this.kycType === KYC_TYPE.SIMPLE) {
              this.backToTarget(true);
            } else {
              this.backToTarget(false);
              // 退出登录
              // this.logoutGame();
            }
          },
        });
      };

      if (this.kycType == KYC_TYPE.FULL && !this.isInRegisterPeriod) {
        dialogFn();
      } else {
        if (this.kycStatus == KYC_STATUS.Hundred) {
          dialogFn();
        } else {
          useAutoPopMgrStore().showKycVerifyTip = true;
        }
      }
    },

    /**
     * 审核拒绝
     */
    rejectPop() {
      // 简版、关闭状态的 KYC弹窗， 只在提现和登录时显示
      if (this.kycType == KYC_TYPE.SIMPLE || this.kycType == KYC_TYPE.CLOSE) {
        if (![KYC_SCENE.Withdraw, KYC_SCENE.Login].includes(this.inSceneType)) {
          return;
        }
      }
      //简版 KYC弹窗， 在登录时可以通过配置控制是否显示
      if (
        !this.showKycLoginDalog &&
        this.inSceneType == KYC_SCENE.Login &&
        this.kycType == KYC_TYPE.SIMPLE
      ) {
        return;
      }
      this.showKycLoginDalog = false;
      this.poping = true;

      const dialogFn = () => {
        const $dialog = getGlobalDialog();
        $dialog({
          title: "KYC Verification",
          message: `<div style="text-align:center">Review failed</div>`,
          describe: this.rejectMsg,
          confirmText: "Verify Again",
          onConfirm: async () => {
            //拒绝之后不用验证 输入手机号之类的 直接弹出kyc
            this.openKycVerify();
          },
          onCancel: () => {
            if (this.kycType === KYC_TYPE.SIMPLE) {
              this.backToTarget(true);
            } else {
              this.backToTarget(false);
              // 退出登录
              // this.logoutGame();
            }
          },
        });
      };
      if (this.kycType == KYC_TYPE.FULL && !this.isInRegisterPeriod) {
        dialogFn();
      } else {
        useAutoPopMgrStore().showKycVerifyTip = true;
      }
    },

    //提现之前的弹窗
    withdrawPop() {
      this.showKycLoginDalog = false;
      this.poping = true;
      const $dialog = getGlobalDialog();
      $dialog({
        title: "Personal Information",
        message: `<div style="margin-bottom:10px;text-align:center;color:#000">Your account is not yet fully verified.</div> `,
        describe: ` <div style="color:#666;text-align:center;">
              <div>Please complete your registration information.</div>
        `,
        confirmText: "Verify Now",
        onConfirm: async () => {
          //验证是否 绑定手机号
          this.openKycVerify();
        },
        onCancel: () => {
          if (this.kycType === KYC_TYPE.SIMPLE) {
            this.backToTarget(true);
          } else {
            this.backToTarget(false);
            // 退出登录
            // this.logoutGame();
          }
        },
      });
    },

    /**
     *  审核中
     */
    reviewPop() {
      this.poping = true;
      const $dialog = getGlobalDialog();
      $dialog({
        title: "KYC Verification",
        message: `<div style="margin-bottom:10px;">
         <div  style="color:#AC1140;">We are reviewing your application.</div>
         <div style="color:#222;margin-top:10px;">Estimated review time: 30 mins.</div>
        </div> `,
        describe: ` <div style="color:#999;">
        <div>Thank you for providing the required information. After the review is completed, we will notify you via an internal message.</div></div>
        `,
        confirmText: "Refresh",
        onConfirm: async () => {
          //  刷新
          this.refreshKycStatus();
        },
        onCancel: () => {
          this.poping = false;
        },
      });
    },

    /**
     * 验证前置条件
     */
    checkPreconditions() {
      const globalStore = useGlobalStore();
      const user = globalStore.userInfo;
      if (!!user?.phone) {
        // 如果 有手机号、登录密码 直接开始kyc
        this.openKycVerify();
      } else {
        this.showVerifyPreconditionsDialog = true;
      }
    },

    // 验证前置条件成功
    handleVerifyPreconditionsSuccess(isAllSuccess: boolean) {
      if (isAllSuccess) {
        this.showVerifyPreconditionsDialog = false;
        this.openKycVerify();
      } else {
        this.showVerifyPreconditionsDialog = true;
      }
    },

    /**
     * 打开 KYC 验证弹窗
     */
    openKycVerify() {
      if (this.CHANEL_TYPE == CHANEL_TYPE.MAYA) {
        return;
      }
      if (this.inSceneType === KYC_SCENE.UNKNOWN) return;
      // 审核拒绝或者 审核完成后 不再弹出
      if (this.kycStatus === KYC_STATUS.COMPLETE || this.kycStatus === KYC_STATUS.REVIEWING) return;
      // 特殊处理下
      if (this.kycStatus === KYC_STATUS.Hundred) {
        router.push(`/kyc/full-form`);
        return;
      }
      // 跳转kyc 验证弹窗
      if (this.kycType === KYC_TYPE.SIMPLE) {
        router.push(`/kyc/simple-form`);
      } else if (this.kycType === KYC_TYPE.FULL) {
        router.push(`/kyc/full-form`);
      } else if (this.kycType === KYC_TYPE.CLOSE || this.kycType === KYC_TYPE.STANDING) {
        router.push(`/kyc/simple-form`);
      }
    },

    logoutGame() {
      const globalStore = useGlobalStore();
      globalStore.loginOut();
    },

    //KYC 展示
    isNeedShowPopKYC() {
      // 都不自动弹出
      return false;

      if (!this._isLoggedIn()) return false;
      // 如果是非web 渠道，不弹出
      if (this.CHANEL_TYPE !== CHANEL_TYPE.WEB) {
        return false;
      }
      // 未认证、审核拒绝
      if ([KYC_STATUS.NO_VERIFY, KYC_STATUS.REJECTED].includes(this.kycStatus)) {
        // 简版、登录页过来会弹窗
        // if (this.kycType == KYC_TYPE.SIMPLE && window["showKycDialogFormLoginPage"]) {
        //   return true;
        // }
        if (this.kycType == KYC_TYPE.SIMPLE) {
          return true;
        }
        if (this.kycType == KYC_TYPE.FULL) {
          return true;
        }
      }
      return false;
    },
  },
});

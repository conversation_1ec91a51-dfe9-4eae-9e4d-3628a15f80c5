import { App } from "vue";
import ZImage from "@/components/ZImage/index.vue";
import XPage from "@/components/Single-purpose/Xpage.vue";
import ZPage from "@/components/ZPage/index.vue";
import ZButton from "@/components/ZButton/index.vue";
import ZDialog from "@/components/ZDialog/index.vue";
import ZActionSheet from "@/components/ZActionSheet/index.vue";
import ZTransition from "@/components/ZTransition/index.vue";
import ZLoadBox from "@/components/ZLoadBox/index.vue";
import ZNoData from "@/components/ZNoData/index.vue";
import ZIcon from "@/components/ZIcon/index.vue";
import ZLoading from "@/components/ZLoading/index.vue";
import ZSelect from "@/components/ZSelect/index.vue";
import ZRadio from "@/components/ZRadio/index.vue";
import ZRadioGroup from "@/components/ZRadioGroup/index.vue";
import ZPopOverlay from "@/components/ZPopOverlay/index.vue";
import ZFloatingBubble from "@/components/ZFloatingBubble/index.vue";
import ZPasswordInput from "@/components/ZPasswordInput/index.vue";
import ZFormField from "@/components/ZFormField/index.vue";
import ZAccountCard from "@/components/ZAccountCard/index.vue";
import ZWithdrawAccountCard from "@/components/ZAccountCard/ZWithdrawAccountCard.vue";
import AddIcon from "@/components/ZComonImg/AddIcon.vue";
import TabBarGap from "@/components/tabbar/Gap.vue";
import IconCoin from "@/components/icons/IconCoin.vue";
import Balance from "@/components/Balance.vue";
import ZFootPng from "@/components/ZFootPng/index.vue";

export function registerGlobalComponents(app: App) {
  app.component("IconCoin", IconCoin);
  app.component("ZImage", ZImage);
  app.component("XPage", XPage);
  app.component("ZPage", ZPage);
  app.component("ZButton", ZButton);
  app.component("ZDialog", ZDialog);
  app.component("ZPopOverlay", ZPopOverlay);
  app.component("ZActionSheet", ZActionSheet);
  app.component("ZTransition", ZTransition);
  app.component("ZLoadBox", ZLoadBox);
  app.component("ZNoData", ZNoData);
  app.component("ZIcon", ZIcon);
  app.component("ZSelect", ZSelect);
  app.component("ZRadio", ZRadio);
  app.component("ZRadioGroup", ZRadioGroup);
  app.component("ZFloatingBubble", ZFloatingBubble);
  app.component("ZPasswordInput", ZPasswordInput);
  app.component("ZLoading", ZLoading);
  app.component("ZAccountCard", ZAccountCard);
  app.component("ZWithdrawAccountCard", ZWithdrawAccountCard);
  app.component("AddIcon", AddIcon);
  app.component("TabBarGap", TabBarGap);
  app.component("Balance", Balance);
  app.component("ZFootPng", ZFootPng);
  app.component("ZFormField", ZFormField);
}

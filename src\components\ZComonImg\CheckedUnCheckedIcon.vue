<!-- 选中、未选中状态图标组件 -->
<template>
  <div
    class="checked-unchecked-icon"
    :class="{ 'is-checked': isChecked }"
    :style="iconWrapperStyle"
  >
    <ZIcon v-show="isChecked" type="icon-success" color="#fff" :size="checkSize" />
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";

interface Props {
  /** 图标大小 */
  size?: number;
  checkSize?: number;
  /** 是否选中状态 */
  isChecked?: boolean;
  /** 外部传入的颜色值 */
  color: string;
}

const props = withDefaults(defineProps<Props>(), {
  size: 20,
  checkSize: 10,
  isChecked: false,
});

// 计算图标配置
const iconConfig = computed(() => {
  return {
    color: props.color,
  };
});

// 计算包装器样式
const iconWrapperStyle = computed(() => ({
  width: `${props.size}px`,
  height: `${props.size}px`,
  background: iconConfig.value.color,
}));
</script>

<style lang="scss" scoped>
.checked-unchecked-icon {
  // background-color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  // 可以添加一些交互效果
  &.is-checked {
    transform: scale(1.05);
  }

  // 悬停效果（如果需要）
  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}
</style>

// src/utils/GlobalScript.ts
import { useGlobalStore } from "@/stores/global";
import { CHANEL_TYPE } from "@/utils/config/GlobalConstant";

export default class Global {
  public t_channel = "web"; //默认是web 如果是ios 或者android  google就是原生了
  public isNative = false; //这个参数区别是否 打包成原生模式
  static instance: Global = null;

  private constructor() {}

  public static getInstance(): Global {
    if (!Global.instance) {
      Global.instance = new Global();
      Global.instance.init();
    }
    return Global.instance;
  }

  private init(): void {
    console.log("GlobalScript init");
  }

  public globalStore(): any {
    return useGlobalStore();
  }

  public setToNative(channel?: string): void {
    this.isNative = true;
    this.t_channel = channel;
    window["isNative_error"] = true;
    switch (channel) {
      case "google":
        Global.getInstance().globalStore().setChannel(CHANEL_TYPE.GOOGLE_PLAY);
        break;
      case "ios":
        Global.getInstance().globalStore().setChannel(CHANEL_TYPE.IOS);
        break;
      case "android":
        Global.getInstance().globalStore().setChannel(CHANEL_TYPE.ANDROID);
        break;
      default:
        break;
    }
    console.log(channel, "----渠道打印----", this.globalStore().channel);
  }
}

import { defineStore } from "pinia";
import { getYearsoldWithDate, stringToInt } from "@/utils/core/tools";
import { KYC_STEP } from "@/views/kyc/CONSTANT";
import { getGlobalDialog } from "@/enter/vant";
import router from "@/router";
import { postKycSubmit, getKycData } from "@/api/kyc";
import { useKycMgrStore, KYC_TYPE } from "@/stores/kycMgr";
import { useGlobalStore } from "@/stores/global";
import { getLocalStorage, removeLocalStorage, setLocalStorage } from "@/utils/core/Storage";
import { isEmpty } from "lodash-es";
import {
  executeVerification,
  type VerificationResult,
  VERIFICATION_TYPES,
} from "@/utils/VerificationMgr";

function getDynamicStorageKey() {
  try {
    const globalStore = useGlobalStore();
    const phone = globalStore.userInfo?.phone;
    return phone ? `KYC_FORM_${phone}` : "KYC_FORM_DEFAULT";
  } catch (error) {
    return "KYC_FORM_DEFAULT";
  }
}

// 校验日期输入
export function vaildDateInputErr(field: string, value: any) {
  let errStr = "";
  if (["day", "month", "year"].includes(field)) {
    if (!value) {
      errStr = "Date of Birth cannot be empty";
    }
  }
  if (field === "day") {
    const day = stringToInt(value);
    if (day < 1 || day > 31) {
      errStr = "Please enter a correct date";
    }
  }
  if (field === "month") {
    const month = stringToInt(value);
    if (month < 1 || month > 12) {
      errStr = "Please enter a correct month";
    }
  }
  if (field === "year") {
    const year = stringToInt(value);
    const nowYear = new Date().getFullYear();
    if (year < 1920 || year > nowYear) {
      errStr = "Please enter a correct year";
    }
  }
  return errStr;
}

// 合法的年月日
export const checkLegalDate = (field: string, value: any, formData: any) => {
  let errStr = vaildDateInputErr(field, value);
  if (!errStr && getYearsoldWithDate(formData.year, formData.month, formData.day) < 21) {
    errStr = "Under 21 years old";
  }
  return errStr;
};

export const StatusStr = ["Login form", "Complete form", "Simple form", "Standing KYC"];

export enum PHOTO_TYPE {
  UP_ID_PHOTO = 0,
  UP_ID_PHOTO_HOLDING,
}

// 详版表单初始值
export const initFullFormData = {
  first_name: "",
  middle_name: "",
  last_name: "",
  day: "",
  month: "",
  year: "",
  is_no_goverment: "1",
  place_of_birth: "",
  branch: "",
  nationality: "",
  current_address: "",
  permanent_address: "",
  work: "",
  income: "",
  country: "Philippines",
  id_type: "",
  account_type: "",
  account_no: "",
  font_side_url: "",
  selfie_picture_url: "",
};
// 简版表单初始值
const initSimpleFormData = {
  first_name: "",
  middle_name: "",
  last_name: "",
  day: "",
  month: "",
  year: "",
  font_side_url: "",
  selfie_picture_url: "",
};

export const useKycStore = defineStore("kyc", {
  state: () => ({
    fullFormData: { ...initFullFormData }, // 详版表单
    simpleFormData: { ...initSimpleFormData }, // 简版表单
    detailDayInputErrTip: "", // 详版-出生年月错误提示
    simpleDayInputErrTip: "", // 简版-出生年月错误提示
    isSameCurrentAddress: true, // 详版-原先地址和当前地址一致
    isGovemmentOfficial: true, // 详版-政府官员
    curStep: KYC_STEP.KYC_STEP_NAME, // 详版-当前步骤
  }),
  getters: {
    globalStore() {
      return useGlobalStore();
    },
    kycMgrStore() {
      return useKycMgrStore();
    },
  },
  actions: {
    // 详版：上一步
    handlePreStep() {
      switch (this.curStep) {
        case KYC_STEP.KYC_STEP_ADDRESS:
          this.curStep = KYC_STEP.KYC_STEP_NAME;
          break;
        case KYC_STEP.KYC_STEP_PHOTO:
          this.curStep = KYC_STEP.KYC_STEP_ADDRESS;
          break;
        case KYC_STEP.KYC_STEP_NAME:
          this.handleLeavePage();
          break;
        default:
          break;
      }
      this.saveUserKycData();
    },
    // 下一步
    handleNextStep() {
      switch (this.curStep) {
        case KYC_STEP.KYC_STEP_NAME:
          this.curStep = KYC_STEP.KYC_STEP_ADDRESS;
          break;
        case KYC_STEP.KYC_STEP_ADDRESS:
          this.curStep = KYC_STEP.KYC_STEP_PHOTO;
          break;
        case KYC_STEP.KYC_STEP_PHOTO:
          this.handleDetailSubmit();
          break;
        default:
          break;
      }
      this.saveUserKycData();
    },
    // 离开页面
    handleLeavePage() {
      const $dialog = getGlobalDialog();
      $dialog({
        title: "Tips",
        message: "Are you sure you want to cancel your registration?",
        confirmText: "Confirm",
        cancelText: "Cancel",
        onConfirm: async () => {
          try {
            router.back();
          } catch (err) {
            router.replace("/");
          }
        },
      });
    },
    // 更新详版照片
    updateDetailPhotoBase64(type: PHOTO_TYPE, fontSideUrl) {
      if (type === PHOTO_TYPE.UP_ID_PHOTO) {
        this.fullFormData.font_side_url = fontSideUrl;
      } else if (type === PHOTO_TYPE.UP_ID_PHOTO_HOLDING) {
        this.fullFormData.selfie_picture_url = fontSideUrl;
      }
      this.saveUserKycData();
    },
    // 更新简版照片
    updateSimplePhotoBase64(type: PHOTO_TYPE, fontSideUrl) {
      if (type === PHOTO_TYPE.UP_ID_PHOTO) {
        this.simpleFormData.font_side_url = fontSideUrl;
      } else if (type === PHOTO_TYPE.UP_ID_PHOTO_HOLDING) {
        this.simpleFormData.selfie_picture_url = fontSideUrl;
      }
      this.saveUserKycData();
    },
    // 同步当前地址到原先地址
    handleSameAddressChecked() {
      if (this.isSameCurrentAddress) {
        // 选中时，同步当前地址到永久地址
        this.fullFormData.permanent_address = this.fullFormData.current_address;
      } else {
        // 取消选中时，清空永久地址
        this.fullFormData.permanent_address = "";
      }
      this.saveUserKycData();
    },
    // 下拉选择赋值
    handleSelectConfirm(field: keyof typeof initFullFormData, data: { value: string }) {
      if (field) {
        this.fullFormData[field] = data.value;
      }
      this.saveUserKycData();
    },
    // 详版表单字段更新
    updateDetailFormData(field: keyof typeof initFullFormData, value: any) {
      if (typeof this.fullFormData !== "object" || this.fullFormData === null) {
        this.fullFormData = { ...initFullFormData };
      }
      let errStr = checkLegalDate(field, value, this.fullFormData);
      this.detailDayInputErrTip = errStr;
      this.fullFormData[field] = value;
      // 同步当前地址到原先地址
      if (field === "current_address" && this.isSameCurrentAddress) {
        this.fullFormData.permanent_address = this.fullFormData.current_address;
      }
      this.saveUserKycData();
    },
    // 简版表单字段更新
    updateSimpleFormData(field: keyof typeof initSimpleFormData, value: any) {
      if (typeof this.simpleFormData !== "object" || this.simpleFormData === null) {
        this.simpleFormData = { ...initSimpleFormData };
      }
      let errStr = checkLegalDate(field, value, this.simpleFormData);
      this.simpleDayInputErrTip = errStr;
      this.simpleFormData[field] = value;
      this.saveUserKycData();
    },
    // 详版表单提交
    async handleDetailSubmit() {
      executeVerification(
        VERIFICATION_TYPES.kyc_form_submit_code,
        async (result: VerificationResult | false) => {
          if (result && result.success) {
            await postKycSubmit({
              ...this.fullFormData,
              acc_no: "0" + this.globalStore.userInfo.phone,
              stage: 2,
              status: "Complete form",
            });

            // 更新状态
            this.kycMgrStore.fetchKycState();

            // reset data
            this.curStep = KYC_STEP.KYC_STEP_NAME;
            this.fullFormData = { ...initFullFormData };
            this.detailDayInputErrTip = "";
            this.isSameCurrentAddress = true;
            this.isGovemmentOfficial = true;

            this.clearUserKycData();
            router.replace("/kyc/success");
          }
        }
      );
    },
    // 简版表单提交
    async handleSimpleSubmit() {
      executeVerification(
        VERIFICATION_TYPES.kyc_form_submit_code,
        async (result: VerificationResult | false) => {
          if (result && result.success) {
            await postKycSubmit({
              ...this.simpleFormData,
              acc_no: "0" + this.globalStore.userInfo.phone,
              stage: 2,
              status: this.kycMgrStore.kycType == KYC_TYPE.SIMPLE ? "Standing KYC" : "Simple form",
            });
            // 更新状态
            this.kycMgrStore.fetchKycState();

            // reset data
            this.simpleFormData = { ...initSimpleFormData };
            this.simpleDayInputErrTip = "";

            this.clearUserKycData();
            router.replace("/kyc/success");
          }
        }
      );
    },

    // 保存用户特定的数据到 localStorage
    saveUserKycData() {
      const key = getDynamicStorageKey();
      const data = {
        fullFormData: this.fullFormData,
        simpleFormData: this.simpleFormData,
      };
      setLocalStorage(key, data);
    },

    // 加载用户数据
    async loadUserKycData() {
      let serverData = await getKycData();
      console.log("serverData", serverData);
      const key = getDynamicStorageKey();
      const localData = getLocalStorage(key);

      let mergeData = localData;
      try {
        if (isEmpty(serverData)) {
          mergeData = localData;
        } else {
          mergeData = {
            fullFormData: serverData,
            simpleFormData: serverData,
          };
        }

        this.fullFormData = { ...initFullFormData, ...mergeData.fullFormData };
        this.simpleFormData = { ...initSimpleFormData, ...mergeData.simpleFormData };
      } catch (error) {
        console.error("加载 KYC 数据失败:", error);
      }
    },

    // 清除用户特定的数据
    clearUserKycData() {
      const key = getDynamicStorageKey();
      removeLocalStorage(key);
    },
  },
});

# 强制更新弹窗实现说明 - $dialog 函数组件版本

## 概述

根据您的需求，我已经完成了强制更新弹窗逻辑的修改，改为使用 `$dialog` 函数组件的形式，实现专门接口获取和定时轮询，弹窗出现在21岁提示弹窗后。

## 实现的功能特点

✅ **$dialog 函数组件**：使用 `getGlobalDialog()` 创建强制更新弹窗  
✅ **专门接口获取**：使用 `api/user.ts` 下的 `getVersionInfo` 接口  
✅ **定时轮询**：参考 cocos 代码实现定时器逻辑  
✅ **弹窗顺序**：强制更新弹窗在21岁提示弹窗后显示（优先级14）  
✅ **错误处理**：当接口返回426错误码时触发强制更新弹窗  
✅ **轮询间隔**：从接口响应的 `refresh_interval` 字段获取轮询间隔  

## 核心实现

### 1. `$dialog` 强制更新弹窗

在 `src/stores/autoPopMgr.ts` 中实现：

```typescript
/** 显示强制更新弹窗 */
showForceUpdateDialog() {
  const $dialog = getGlobalDialog();
  
  $dialog({
    title: "Found a new version!",
    message: "You've stayed on this page too long. Please refresh to get the latest version.",
    showCancelButton: false,
    showClose: false,
    zIndex: 99999,
    confirmText: "Update Now",
    onConfirm: () => {
      window.location.reload();
    },
    onClose: () => {
      window.location.reload();
    },
  });
}
```

### 2. 版本检查定时器

```typescript
async startVersionCheckTimer() {
  try {
    const response = await getVersionInfo();
    const data = response?.data || response;
    
    if (data?.refresh_interval) {
      this.checkVersionTime = data.refresh_interval * 1000;
    }
    
    this.versionCheckTimer = setTimeout(() => {
      this.startVersionCheckTimer();
    }, this.checkVersionTime);
  } catch (error: any) {
    if (error?.response?.status === 426 || error?.code === 426) {
      this.needForceUpdate = true;
      this.showForceUpdateDialog();
    }
  }
}
```

## 修改的文件

### 1. `src/stores/autoPopMgr.ts`
- 添加了 `showForceUpdateDialog()` 方法，使用 `$dialog` 显示弹窗
- 实现了版本检查定时器逻辑
- 在426错误时直接调用 `showForceUpdateDialog()`
- 移除了不再需要的 `showForceUpdateDialog` 状态变量

### 2. `src/utils/AutoPopMgr.ts`
- 修改强制更新弹窗的显示逻辑，调用 `showForceUpdateDialog()` 方法

### 3. `src/views/home/<USER>
- 移除了 `ForceUpdateDialog` 组件的导入和使用
- 移除了相关的处理函数
- 保留了版本检查定时器的启动和停止逻辑

## 工作流程

1. **启动定时器**：用户登录后，在 Home.vue 中启动版本检查定时器
2. **定时轮询**：定时器调用 `getVersionInfo` 接口检查版本
3. **获取间隔**：从接口响应的 `refresh_interval` 字段获取下次检查的间隔时间
4. **错误检测**：如果接口返回426错误码，直接调用 `showForceUpdateDialog()`
5. **显示弹窗**：使用 `$dialog` 函数显示强制更新弹窗
6. **用户操作**：用户点击更新按钮后刷新页面

## 测试工具

创建了 `src/utils/test-force-update.ts` 测试工具，在开发环境下可以使用：

```javascript
// 在浏览器控制台中使用
window.forceUpdateTester.triggerForceUpdate()  // 直接显示强制更新弹窗
window.forceUpdateTester.simulate426Error()    // 模拟426错误触发流程
window.forceUpdateTester.testVersionCheckTimer()  // 测试版本检查定时器
window.forceUpdateTester.getCurrentState()     // 获取当前状态
```

## 优势

1. **轻量级**：使用 `$dialog` 函数组件，无需额外的 Vue 组件
2. **统一样式**：与系统其他弹窗保持一致的样式
3. **简化逻辑**：减少了状态管理的复杂性
4. **易于维护**：代码更加简洁，逻辑更加清晰

## 注意事项

- 强制更新弹窗不可通过遮罩关闭，只能点击更新按钮
- 弹窗会强制刷新页面，确保用户获取最新版本
- 版本检查定时器在用户登录后自动启动，页面卸载时自动停止

import type { WithdrawAccount } from "@/hooks/useWithdrawAccounts";
import http from "@/utils/http";

export interface WithdrawAccountList {
  list: WithdrawAccount[];
  current_num: number;
  total_num: number;
}

// 获取提现账号列表
export const getWithdrawAccounts = () => {
  return http.post<WithdrawAccountList>("/common/api/get/withdraw/list", {}, { type: "formData" });
};

// 添加提现账号
export const addWithdrawAccount = (data: WithdrawAccount) => {
  return http.post("/common/api/add/withdraw/no", data, {
    type: "formData",
    transformResult: (res) => res.data,
  });
};

// 更新提现账号
export const updateWithdrawAccount = (data: WithdrawAccount) => {
  return http.post("/common/api/update/account/info", data, {
    transformResult: (res) => res.data,
  });
};

// 提现
export const goExchange = (data: any) => {
  return http.post("/common/api/exchange", data, {
    transformResult: (res) => res.data,
  });
};

// 1倍流水风控
export const checkWithdrawRisk = (data: any) => {
  return http.post("/common/api/withdraw/risk", data, { transformResult: (res) => res.data });
};

// 支付密码确认
export const confirmWithdrawPassword = (data: any) => {
  return http.post("/common/api/confirm/withdraw/password", data, {
    transformResult: (res) => res.data,
  });
};

// 获取银行列表
export const getPaycoolsBanks = (data: any) => {
  return http.post("/open/api/paycools/banks", data, {});
};

/**
 * 聚焦管理工具
 * 用于处理弹窗切换时的输入框聚焦时机，避免键盘弹出导致的页面跳动
 */

export class FocusManager {
  private static instance: FocusManager;
  private focusQueue: Array<() => void> = [];
  private isTransitioning = false;
  private transitionTimer: NodeJS.Timeout | null = null;

  static getInstance(): FocusManager {
    if (!FocusManager.instance) {
      FocusManager.instance = new FocusManager();
    }
    return FocusManager.instance;
  }

  /**
   * 标记开始弹窗切换
   * @param duration 切换持续时间，默认400ms
   */
  startTransition(duration = 400) {
    this.isTransitioning = true;

    if (this.transitionTimer) {
      clearTimeout(this.transitionTimer);
    }

    this.transitionTimer = setTimeout(() => {
      this.isTransitioning = false;
      this.processFocusQueue();
    }, duration);
  }

  /**
   * 请求聚焦
   * @param focusCallback 聚焦回调函数
   * @param delay 额外延迟时间，默认100ms
   */
  requestFocus(focusCallback: () => void, delay = 100) {
    if (this.isTransitioning) {
      // 如果正在切换，加入队列
      this.focusQueue.push(focusCallback);
    } else {
      // 否则延迟执行
      setTimeout(focusCallback, delay);
    }
  }

  /**
   * 立即聚焦（跳过队列）
   * @param focusCallback 聚焦回调函数
   */
  immediateFocus(focusCallback: () => void) {
    focusCallback();
  }

  /**
   * 处理聚焦队列
   */
  private processFocusQueue() {
    if (this.focusQueue.length > 0) {
      // 只执行最后一个聚焦请求
      const lastFocus = this.focusQueue.pop();
      this.focusQueue = []; // 清空队列

      if (lastFocus) {
        setTimeout(lastFocus, 100);
      }
    }
  }

  /**
   * 清空聚焦队列
   */
  clearQueue() {
    this.focusQueue = [];
  }
}

/**
 * 智能聚焦 Hook
 * @param autoFocus 是否自动聚焦
 * @param delay 延迟时间
 */
export function useSmartFocus(autoFocus = true, delay = 200) {
  const focusManager = FocusManager.getInstance();

  const smartFocus = (focusCallback: () => void) => {
    if (!autoFocus) return;

    focusManager.requestFocus(focusCallback, delay);
  };

  const immediateFocus = (focusCallback: () => void) => {
    focusManager.immediateFocus(focusCallback);
  };

  const startTransition = (duration?: number) => {
    focusManager.startTransition(duration);
  };

  return {
    smartFocus,
    immediateFocus,
    startTransition,
  };
}

/**
 * 强制更新功能测试工具
 * 用于测试强制更新弹窗的完整流程
 */

import { useAutoPopMgrStore } from '@/stores/autoPopMgr';

export class ForceUpdateTester {
  private autoPopMgrStore = useAutoPopMgrStore();

  /**
   * 模拟触发强制更新弹窗
   * 直接调用 $dialog 显示强制更新弹窗
   */
  triggerForceUpdate() {
    console.log('🧪 测试：触发强制更新弹窗');
    this.autoPopMgrStore.showForceUpdateDialog();
  }

  /**
   * 模拟426错误触发强制更新
   */
  simulate426Error() {
    console.log('🧪 测试：模拟426错误');
    this.autoPopMgrStore.needForceUpdate = true;
    
    // 延迟触发弹窗管理器检查
    setTimeout(() => {
      const { AutoPopMgr } = require('@/utils/AutoPopMgr');
      AutoPopMgr.autoPopupDialog();
    }, 100);
  }

  /**
   * 测试版本检查定时器
   */
  testVersionCheckTimer() {
    console.log('🧪 测试：启动版本检查定时器');
    this.autoPopMgrStore.startVersionCheckTimer();
  }

  /**
   * 停止版本检查定时器
   */
  stopVersionCheckTimer() {
    console.log('🧪 测试：停止版本检查定时器');
    this.autoPopMgrStore.stopVersionCheckTimer();
  }

  /**
   * 重置强制更新状态
   */
  resetForceUpdateState() {
    console.log('🧪 测试：重置强制更新状态');
    this.autoPopMgrStore.needForceUpdate = false;
  }

  /**
   * 获取当前状态
   */
  getCurrentState() {
    return {
      needForceUpdate: this.autoPopMgrStore.needForceUpdate,
      checkVersionTime: this.autoPopMgrStore.checkVersionTime,
      hasTimer: !!this.autoPopMgrStore.versionCheckTimer
    };
  }
}

// 在开发环境下暴露到全局，方便调试
if (process.env.NODE_ENV === 'development') {
  (window as any).forceUpdateTester = new ForceUpdateTester();
  console.log('🧪 强制更新测试工具已加载，使用 window.forceUpdateTester 进行测试');
  console.log('可用方法：');
  console.log('- triggerForceUpdate(): 直接显示强制更新弹窗');
  console.log('- simulate426Error(): 模拟426错误触发流程');
  console.log('- testVersionCheckTimer(): 启动版本检查定时器');
  console.log('- getCurrentState(): 获取当前状态');
}

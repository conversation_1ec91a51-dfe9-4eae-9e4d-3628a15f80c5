<!-- ZFormField 组件演示页面 -->
<template>
  <ZPage title="ZFormField 演示">
    <div class="demo-container">
      <div class="demo-section">
        <h3>基础用法</h3>
        <ZFormField
          v-model="form.name"
          label="姓名"
          placeholder="请输入姓名"
          required
          help-text="请输入您的真实姓名"
        />

        <ZFormField
          v-model="form.email"
          label="邮箱"
          type="email"
          placeholder="请输入邮箱地址"
          :rules="emailRules"
          required
        />
      </div>

      <div class="demo-section">
        <h3>输入限制</h3>
        <ZFormField
          v-model="form.username"
          label="用户名（只允许英文和数字）"
          restriction="alphanumeric"
          placeholder="只能输入英文字母和数字"
          :max-length="20"
          required
        />

        <ZFormField
          v-model="form.phone"
          label="手机号（只允许数字）"
          restriction="phone"
          placeholder="请输入10位手机号"
          required
        />

        <ZFormField
          v-model="form.code"
          label="验证码（只允许数字）"
          restriction="numeric"
          placeholder="请输入6位验证码"
          :max-length="6"
          :min-length="6"
          required
        />
      </div>

      <div class="demo-section">
        <h3>密码输入</h3>
        <ZFormField
          v-model="form.password"
          label="密码"
          type="password"
          placeholder="请输入密码"
          :min-length="8"
          :max-length="20"
          :rules="passwordRules"
          required
          help-text="密码长度8-20位，必须包含字母和数字"
        />

        <ZFormField
          v-model="form.confirmPassword"
          label="确认密码"
          type="password"
          placeholder="请再次输入密码"
          :rules="confirmPasswordRules"
          required
        />
      </div>

      <div class="demo-section">
        <h3>自定义过滤器</h3>
        <ZFormField
          v-model="form.upperCase"
          label="自动转大写"
          placeholder="输入会自动转为大写"
          :input-filter="toUpperCase"
        />

        <ZFormField
          v-model="form.noSpaces"
          label="自动移除空格"
          placeholder="空格会被自动移除"
          :input-filter="removeSpaces"
        />
      </div>

      <div class="demo-section">
        <h3>插槽使用</h3>
        <ZFormField
          v-model="form.amount"
          label="金额"
          restriction="numeric"
          placeholder="请输入金额"
        >
          <template #leftIcon>
            <ZIcon type="icon-qianbao" size="16" color="#666" />
          </template>
          <template #button>
            <ZButton size="small" @click="clearAmount">清除</ZButton>
          </template>
        </ZFormField>
      </div>

      <div class="demo-section">
        <h3>验证状态</h3>
        <ZFormField
          v-model="form.realTimeValidation"
          label="实时验证"
          placeholder="输入时实时验证"
          :validate-on-input="true"
          :rules="realTimeRules"
          required
        />

        <ZFormField
          v-model="form.blurValidation"
          label="失焦验证"
          placeholder="失焦时验证"
          :validate-on-blur="true"
          :rules="blurRules"
          required
        />
      </div>

      <div class="demo-section">
        <h3>禁用和只读</h3>
        <ZFormField
          v-model="form.disabled"
          label="禁用状态"
          placeholder="这是禁用的输入框"
          disabled
        />

        <ZFormField
          v-model="form.readonly"
          label="只读状态"
          placeholder="这是只读的输入框"
          readonly
        />
      </div>

      <div class="demo-actions">
        <ZButton @click="validateAll" type="primary">验证所有字段</ZButton>
        <ZButton @click="clearAll" type="default">清空所有字段</ZButton>
        <ZButton @click="showFormData" type="info">查看表单数据</ZButton>
      </div>

      <div v-if="showData" class="form-data">
        <h4>表单数据：</h4>
        <pre>{{ JSON.stringify(form, null, 2) }}</pre>
      </div>
    </div>
  </ZPage>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { showToast } from "vant";
import ZFormField from "./index.vue";

// 表单数据
const form = reactive({
  name: "",
  email: "",
  username: "",
  phone: "",
  code: "",
  password: "",
  confirmPassword: "",
  upperCase: "",
  noSpaces: "",
  amount: "",
  realTimeValidation: "",
  blurValidation: "",
  disabled: "禁用的值",
  readonly: "只读的值",
});

const showData = ref(false);

// 验证规则
const emailRules = [
  {
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: "请输入有效的邮箱地址",
  },
];

const passwordRules = [
  {
    pattern: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/,
    message: "密码必须包含字母和数字",
  },
];

const confirmPasswordRules = [
  {
    validator: (value: string) => {
      if (value !== form.password) {
        return "两次输入的密码不一致";
      }
      return true;
    },
  },
];

const realTimeRules = [
  {
    pattern: /^[a-zA-Z0-9]{3,}$/,
    message: "至少3位字母或数字",
  },
];

const blurRules = [
  {
    minLength: 5,
    message: "至少输入5个字符",
  },
];

// 自定义过滤器
const toUpperCase = (value: string) => {
  return value.toUpperCase();
};

const removeSpaces = (value: string) => {
  return value.replace(/\s/g, "");
};

// 方法
const clearAmount = () => {
  form.amount = "";
  showToast("金额已清除");
};

const validateAll = () => {
  // 这里可以添加表单验证逻辑
  showToast("验证完成");
};

const clearAll = () => {
  Object.keys(form).forEach((key) => {
    if (key !== "disabled" && key !== "readonly") {
      form[key] = "";
    }
  });
  showToast("表单已清空");
};

const showFormData = () => {
  showData.value = !showData.value;
};
</script>

<style lang="scss" scoped>
.demo-container {
  padding: 16px;
}

.demo-section {
  margin-bottom: 32px;

  h3 {
    margin-bottom: 16px;
    color: #333;
    font-size: 18px;
    font-weight: 600;
    border-bottom: 2px solid #ac1140;
    padding-bottom: 8px;
  }
}

.demo-actions {
  display: flex;
  gap: 12px;
  margin: 24px 0;
  flex-wrap: wrap;
}

.form-data {
  margin-top: 24px;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 8px;

  h4 {
    margin-bottom: 12px;
    color: #333;
  }

  pre {
    background-color: #fff;
    padding: 12px;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 12px;
    line-height: 1.4;
  }
}
</style>

import http from "@/utils/http";
import { useGlobalStore } from "@/stores/global";
import { CHANEL_TYPE } from "@/utils/config/GlobalConstant";

// 获取充值项，判断是否首次充值等
export const getDepositRule = () => {
  return http.post(
    "/common/api/global-config/first/recharge/rule",
    {},
    {
      type: "formData",
    }
  );
};

export const rechargeWithdraw = (data = {}) => {
  const globalStore = useGlobalStore();
  const appChannel = globalStore.channel == CHANEL_TYPE.G_CASH ? "Gcash_h5" : data.appChannel;

  return http.post("/common/api/global-config/recharge-withdraw", {
    appChannel,
  });
};

// 充值
// export const paymentBalanceAdd = (data: object) => {
//   return http.post("/common/api/payment/balance-add", data, {
//     type: "formData",
//     transformResult: (res) => res.data,
//   });
// };

// webPay gcash充值
export const paymentGcash = (data: object) => {
  return http.post("/open/api/gcash/payment", data, {
    type: "formData",
    transformResult: (res) => res.data,
  });
};
// webPay gcash充值
export const paymentMaya = (data: object) => {
  return http.post("/open/api/maya/payment", data, {
    type: "formData",
    transformResult: (res) => res.data,
  });
};
// webPay Iotach充值
export const paymentIotach = (data: object) => {
  return http.post("/open/api/iotach/payment", data, {
    type: "formData",
    transformResult: (res) => res.data,
  });
};
// webPay Paycools充值
export const paymentPaycools = (data: object) => {
  return http.post("/open/api/paycools/payment", data, {
    type: "formData",
    transformResult: (res) => res.data,
  });
};

//gcash内购 跳回小程序
export const gcashSetJumpType = (data = {}) => {
  return http.post("/common/api/gcash/set/jumpType", data, {
    transformResult: (res) => res.data,
  });
};

// 验证是否购买成功
export const verifyPay = (data = {}) => {
  return http.post("/api/pay-service/verify-pay", data);
};

import { defineStore } from "pinia";
import {
  getDepositRule,
  paymentGcash,
  paymentMaya,
  paymentIotach,
  paymentPaycools,
  gcashSetJumpType,
  rechargeWithdraw,
} from "@/api/deposit";

import { useGlobalStore } from "@/stores/global";
import { useKycMgrStore, KYC_SCENE } from "@/stores/kycMgr";

import { useGameStore } from "@/stores/game";
import { getBrowserName, isBrowser } from "@/utils/core/tools";
import {
  CHANEL_TYPE,
  PAY_IDENTIFIER,
  PAY_METHOD,
  MAINTENANCETIPCODE,
} from "@/utils/config/GlobalConstant";
import router from "@/router/index";
import { getEnvConfig } from "@/utils/config/Config";
import { getGlobalDialog } from "@/enter/vant";
import { showToast } from "vant";
import { getLocalStorage, removeLocalStorage, setLocalStorage } from "@/utils/core/Storage";
import { showZLoading, closeZLoading } from "@/utils/ZLoadingAPI";
import { MobileWindowManager } from "@/utils/managers/MobileWindowManager";

const SELECT_PAYMENT = "SELECT_PAYMENT";

// 充值项类型声明
interface RechargeItem {
  id: number;
  name: string;
  account_type: string;
  min: string;
  max: string;
  first_restriction?: string | number;
  sort?: number;
  amount?: number;
  award?: number;
}

export const useDepositStore = defineStore("deposit", {
  state: () => ({
    // 弹窗显示
    show: false,
    // 输入（选中）的充值金额
    selectedAmount: "" as string | number,
    // 错误提示
    errTip: "",
    // 警告提示
    warnTip: "",
    // 充值方式列表
    rechargeList: [] as RechargeItem[],
    // 首次充值限制
    firstRestriction: 0,
    // 是否首充
    isFirstDeposit: false,
    // 首充奖励列表
    firstDepositAwradList: [] as RechargeItem[],
    // 奖励金额
    awardNum: 0,
    // 总金额
    totalAmount: 0,
    // 充值项ID
    rechargeID: 0,
    // 支付方式对应的值
    payAccountTypeValue: null as string | number | null,
    // 当前充值方式的最小充值金额
    minAmount: 0,
    // 当前充值方式的最大充值金额
    maxAmount: 0,
    // 定时器
    depositTimeout: null as ReturnType<typeof setTimeout> | null,
  }),
  getters: {
    // 渠道来源
    CHANEL_TYPE(): CHANEL_TYPE {
      const globalStore = useGlobalStore();
      return globalStore.channel;
    },
    // 是否小程序端，即 G_CASH、MAYA 端
    isMiniChannel(): boolean {
      const globalStore = useGlobalStore();
      const channel = globalStore.channel?.toLowerCase();
      return ["gcash", "maya"].includes(channel);
    },
    $dialog() {
      return getGlobalDialog();
    },
    kycMgrStore() {
      return useKycMgrStore();
    },
  },
  actions: {
    // 打开充值入口
    async openDialog(depositNum?: number | string | PointerEvent) {
      const globalStore = useGlobalStore();
      if (!globalStore.token) {
        router.push("/login");
        return;
      }
      // KYC验证
      this.kycMgrStore.verifyKyc(KYC_SCENE.Deposit, async (isVerity) => {
        if (isVerity) {
          showZLoading();
          try {
            // 初始化数据
            await this.init();
            // 处理参数，兼容数字和字符串数字，排除事件对象
            if (
              depositNum &&
              (typeof depositNum === "number" ||
                (typeof depositNum === "string" && !isNaN(Number(depositNum))))
            ) {
              this.setSelectedAmount(depositNum);
            } else {
              this.selectedAmount = "";
            }
            // 没有web充值渠道，走小程序
            if (this.CHANEL_TYPE === CHANEL_TYPE.G_CASH && !this.existDepositMethod()) {
              this.backMiniBuyTips();
              closeZLoading();
              return;
            }
            // 打开弹窗
            this.show = true;
          } catch (error) {
          } finally {
            closeZLoading();
          }
        }
      });
    },
    // 存在web充值方式
    existDepositMethod() {
      let exist = false;
      const rechargeConfig = this.rechargeList;
      if (rechargeConfig?.length > 0) exist = true;
      return exist;
    },
    // 请求入口
    async init() {
      try {
        if (!this.isMiniChannel) {
          await this.loadFirstDeposit();
          const amountConfigRes = await this.loadAmountConfig();
          this.initPayMethod(amountConfigRes);
        } else if (this.isMiniChannel) {
          await this.loadFirstDeposit();
          const amountConfigRes = await this.loadAmountConfig();
          this.payAccountTypeValue = amountConfigRes?.[0]?.account_type;
          this.setLabAmount(0);
        }
      } catch (error) {
        console.error("Deposit init failed:", error);
        showToast("The current network is abnormal, please try again later.");
        throw error; // 重新抛出错误，让 openDialog 处理
      }
    },
    // 初始化支付方式
    initPayMethod(amountConfigRes: RechargeItem[]) {
      let lastPayAccountType = getLocalStorage(SELECT_PAYMENT);
      let lastPayAccountIndex = amountConfigRes.findIndex(
        (item) => item.account_type === lastPayAccountType
      );
      if (lastPayAccountIndex > -1) {
        this.payAccountTypeValue = lastPayAccountType;
        this.setLabAmount(lastPayAccountIndex);
        return;
      }
      this.payAccountTypeValue = amountConfigRes?.[0]?.account_type || "";
      this.setLabAmount(0);
    },
    // 设置当前充值方式的金额区间
    setLabAmount(index: number) {
      let minAmount = parseFloat(this.rechargeList[index]?.min);
      let maxAmount = parseFloat(this.rechargeList[index]?.max);
      let first_restriction = this.rechargeList[index]?.first_restriction || 0;
      if (this.isFirstDeposit && parseFloat(first_restriction as string) > 0) {
        minAmount = parseFloat(first_restriction as string);
      }
      this.minAmount = minAmount;
      this.maxAmount = maxAmount;
      this.firstRestriction = Number(first_restriction);
    },
    // 获取是否首充、充值项对应奖励金额
    async loadFirstDeposit() {
      try {
        const response = await getDepositRule();
        const res = response.data || response;

        this.isFirstDeposit = res.is_first_charge || false;
        let rechargeList: RechargeItem[] = res.recharge || [];
        rechargeList.sort((a, b) => a.id - b.id);
        this.firstDepositAwradList = rechargeList;

        if (!this.firstDepositAwradList || this.firstDepositAwradList.length === 0) {
          this.isFirstDeposit = false;
        }
        return res;
      } catch (error) {
        console.error("Load first deposit failed:", error);
        this.isFirstDeposit = false;
        this.firstDepositAwradList = [];
        throw new Error("Failed to load first deposit information");
      }
    },
    // 获取充值方式及金额配置
    async loadAmountConfig() {
      try {
        const response = await rechargeWithdraw({
          appChannel: this.CHANEL_TYPE,
        });
        const res = response;

        let recharge: RechargeItem[] = res.recharge || [];
        recharge.sort((a, b) => {
          if (a.sort !== b.sort) {
            return b.sort! - a.sort!;
          } else {
            if (a.name && b.name) {
              return b.name.localeCompare(a.name);
            }
            return 0;
          }
        });
        this.rechargeList = recharge;
        return recharge;
      } catch (error) {
        console.error("Load amount config failed:", error);
        this.rechargeList = [];
        throw new Error("Failed to load payment methods");
      }
    },
    // 设置当前充值方式
    setCurReChangeName(data: { account_type: string; name: string }) {
      const index = this.rechargeList.findIndex((item) => item.account_type === data.account_type);
      this.payAccountTypeValue = this.rechargeList[index].account_type || "";
      this.setLabAmount(index);
      setLocalStorage(SELECT_PAYMENT, data.account_type);
      // 如果paymethod>6个，调整顺序
      if (this.rechargeList.length > 6) {
        const selectPayItem = this.rechargeList[index];
        this.rechargeList.splice(index, 1);
        this.rechargeList.unshift(selectPayItem);
      }
      this.resetData();
    },
    // 点击选中金额
    setSelectedAmount(amount: string | number) {
      this.selectedAmount = amount;
      this.getValidAmountErr(amount);
      this.calcAmount(+amount);
    },
    // 计算总额和奖励金额
    calcAmount(value: number) {
      if (this.errTip) {
        this.awardNum = 0;
        this.totalAmount = 0;
        return;
      }

      const calValue = this.getFirstBonusNum(value);
      this.awardNum = calValue;
      this.totalAmount = Number(value) + Number(calValue);
    },
    // 输入框 input事件
    handleCustomAmountInput(event: Event) {
      const input = event.target as HTMLInputElement;
      let value = input.value.replace(/\D/g, "");

      // 如果输入为空，直接处理
      if (!value) {
        this.setSelectedAmount("");
        this.getValidAmountErr("");
        this.calcAmount(0);
        return;
      }

      // 金额限制：确保不超过最大限额
      const numValue = Number(value);
      if (this.maxAmount > 0 && numValue > this.maxAmount) {
        value = this.maxAmount.toString();
        // 更新输入框显示值
        input.value = value;
      }

      this.setSelectedAmount(value);
      this.getValidAmountErr(value);
      this.calcAmount(Number(value));
    },
    // 校验金额范围提示
    getValidAmountErr(value: string | number) {
      const min = this.minAmount;
      const max = this.maxAmount;
      const numValue = Number(value);

      // 重置提示状态
      this.errTip = "";
      this.warnTip = "";

      // 空值处理
      if (value === "" || value === null || value === undefined) {
        this.errTip = `Enter Amount ${min} - ${max}₱`;
        return this.errTip;
      }

      // 无效数值处理
      if (isNaN(numValue) || numValue < 0) {
        this.errTip = "Please enter a valid amount";
        return this.errTip;
      }

      // 金额范围验证
      if (numValue < min) {
        this.errTip = `The minimum amount is ${min}₱`;
      } else if (numValue > max) {
        this.errTip = `The maximum amount is ${max}₱`;
      } else if (numValue === max) {
        // 达到最大限额时显示警告
        this.warnTip = `The maximum allowable input is ${max}₱`;
      }

      return this.errTip;
    },
    //获取充值奖励, 获取充值金额对应的id（小于最接近那个充值金额，的充值id）
    getChargeId(chargeNum: number) {
      if (this.firstDepositAwradList.length === 0 || !chargeNum) {
        return 0;
      }
      let totalLen = this.firstDepositAwradList.length;
      if (chargeNum >= (this.firstDepositAwradList[totalLen - 1].amount || 0)) {
        return this.firstDepositAwradList[totalLen - 1].id;
      }
      for (let idx = 0; idx < totalLen; idx++) {
        let element = this.firstDepositAwradList[idx];
        if (chargeNum < (element.amount || 0)) {
          if (idx === 0) {
            return 0;
          } else {
            return this.firstDepositAwradList[idx - 1].id;
          }
        }
      }
      return 0;
    },
    // 获取充值金额对应的award（小于最接近那个充值金额，的充值奖励）
    getFirstBonusNum(chargeNum: number) {
      if (this.firstDepositAwradList.length === 0 || !chargeNum) {
        return 0;
      }
      let totallength = this.firstDepositAwradList.length;
      if (chargeNum >= (this.firstDepositAwradList[totallength - 1].amount || 0)) {
        return this.firstDepositAwradList[totallength - 1].award || 0;
      }
      for (let index = 0; index < totallength; index++) {
        let element = this.firstDepositAwradList[index];
        if (chargeNum < (element.amount || 0)) {
          if (index === 0) {
            return 0;
          } else {
            return this.firstDepositAwradList[index - 1].award || 0;
          }
        }
      }
      return 0;
    },
    //10秒超时处理 关闭响应
    async handleTimeout() {
      this.$dialog({
        title: "Tips",
        message: "Your deposit request is currently being processed. Please kindly wait.",
        confirmText: "Done",
        showCancelButton: false,
        onConfirm: async () => {
          this.show = false;
          router.push("/account/bet-order");
        },
      });
    },
    // 清掉充值超时定时器
    stopDepTimeoutSchedule() {
      if (this.depositTimeout) {
        clearTimeout(this.depositTimeout);
        this.depositTimeout = null;
      }
    },
    // GCash mini-program充值提醒
    backMiniBuyTips() {
      this.$dialog({
        title: "Quick Reminder",
        message: "Please click 'Go Deposit' to return to the GCash mini-program for top-up.",
        confirmText: "Go Deposit",
        showClose: true,
        showCancelButton: false,
        onConfirm: async () => {
          try {
            const response = await gcashSetJumpType({
              type: this.selectedAmount ? parseInt(this.selectedAmount) : 10,
            });
            const { code } = response;

            if (code === 200 || code === 0) {
              // 使用统一的外跳方法
              try {
                await MobileWindowManager.launchExternalGame(
                  async () => getEnvConfig().VITE_GCASH_SHOP_URL,
                  (error) => {
                    console.error("Failed to open GCash URL:", error);
                    showToast("Failed to open GCash, please try again");
                  }
                );
                this.show = false;
              } catch (error) {
                console.error("GCash launch error:", error);
                showToast("Failed to open GCash, please try again");
              }
            } else if (code === MAINTENANCETIPCODE) {
              this.show = false;
              router.push("/system/maintenance");
            } else {
              throw new Error("GCash jump setup failed");
            }
          } catch (error) {
            console.error("GCash jump failed:", error);
            this.$dialog({
              title: "Tips",
              message: "Failed to setup GCash redirect. Please try again.",
              confirmText: "Done",
              showCancelButton: false,
            });
          }
        },
      });
    },

    // 提交充值
    async handleSubmit() {
      if (this.isMiniChannel) {
        // G-Cash 充值方式,跳转到Gcash 小程序
        if (this.CHANEL_TYPE === CHANEL_TYPE.G_CASH && !this.existDepositMethod()) {
          this.backMiniBuyTips();
          return;
        }
        //判定是否是首充
        if (this.isFirstDeposit) {
          this.rechargeID = this.getChargeId(Number(this.selectedAmount));
          this.awardNum = this.getFirstBonusNum(Number(this.selectedAmount));
        } else {
          this.rechargeID = 0;
          this.awardNum = 0;
        }
      } else if (this.CHANEL_TYPE === CHANEL_TYPE.WEB) {
        this.rechargeID = 0;
        this.awardNum = 0;
      }
      const globalStore = useGlobalStore();

      const params: Record<string, any> = {
        app_package_name: globalStore.userInfo.app_package_name,
        app_version: __APP_VERSION__,
        amount: +this.selectedAmount,
        award: +this.awardNum,
        email: globalStore.payAccount.email,
        phone: globalStore.userInfo.phone,
        name: globalStore.userInfo.nickname,
        config_id: this.rechargeID,
        platform: getBrowserName() + "_" + (isBrowser ? 1 : 0),
      };

      let apiFn: any = "";
      if (this.payAccountTypeValue == null) console.error("payAccountTypeValue is null!!!");

      //支付渠道
      if (this.payAccountTypeValue == PAY_METHOD.MAYA_WEB) {
        apiFn = paymentMaya;
        params["identifier"] = PAY_IDENTIFIER.MAYA_WEB;
      } else if (this.payAccountTypeValue == PAY_METHOD.GCASH_WEB) {
        apiFn = paymentGcash;
        params["identifier"] = PAY_IDENTIFIER.GCASH_WEB;
      } else if (this.payAccountTypeValue == PAY_METHOD.IOT_GCASH) {
        apiFn = paymentIotach;
        params["identifier"] = PAY_IDENTIFIER.IOT_GCASH;
      } else if (this.payAccountTypeValue == PAY_METHOD.IOT_QRPH) {
        apiFn = paymentIotach;
        params["identifier"] = PAY_IDENTIFIER.IOT_QRPH;
      } else if (this.payAccountTypeValue == PAY_METHOD.IOT_PALAWANPAY) {
        apiFn = paymentIotach;
        params["identifier"] = PAY_IDENTIFIER.IOT_PALAWANPAY;
      } else if (this.payAccountTypeValue == PAY_METHOD.PAYCOOLS_BANK) {
        apiFn = paymentPaycools;
        params["identifier"] = PAY_IDENTIFIER.PAYCOOLS_BANK;
      } else if (this.payAccountTypeValue == PAY_METHOD.PAYCOOLS_QR) {
        apiFn = paymentPaycools;
        params["identifier"] = PAY_IDENTIFIER.PAYCOOLS_QR;
      }
      // 10秒超时
      if (!this.depositTimeout) {
        this.depositTimeout = setTimeout(this.handleTimeout, 10000);
      }
      try {
        const response = await apiFn(params);
        const { code, data, msg } = response;

        this.stopDepTimeoutSchedule();

        if (code === 200 || code === 0) {
          if (!this.isMiniChannel) {
            if (data?.paymentUrl) {
              this.show = false;
              // 使用统一的外跳方法打开支付页面
              try {
                await MobileWindowManager.launchExternalGame(
                  async () => data.paymentUrl,
                  (error) => {
                    console.error("Failed to open payment URL:", data.paymentUrl, error);
                    showToast("Failed to open payment page, please try again");
                  }
                );
              } catch (error) {
                console.error("Payment URL launch error:", error);
                showToast("Failed to open payment page, please try again");
              }
            }
          }
          if (this.isMiniChannel) {
            if (this.CHANEL_TYPE == CHANEL_TYPE.G_CASH) {
              if (data.paymentUrl) {
                this.show = false;
                // 使用统一的外跳方法打开支付页面
                try {
                  await MobileWindowManager.launchExternalGame(
                    async () => data.paymentUrl,
                    (error) => {
                      console.error("Failed to open payment URL:", data.paymentUrl, error);
                      showToast("Failed to open payment page, please try again");
                    }
                  );
                } catch (error) {
                  console.error("Payment URL launch error:", error);
                  showToast("Failed to open payment page, please try again");
                }
              }
            } else {
              let str = "Deposit Successful! Enjoy Your Nustar Online Gaming Experience!";
              if (this.isFirstDeposit) {
                str =
                  "You have successfully claimed your bonus. Best of luck on your journey ahead!";
              }
              this.$dialog({
                title: "Congratulations",
                message: str,
                confirmText: "Bet Now",
                onConfirm: async () => {
                  useGameStore().clickBetNow(() => (this.show = false));
                },
              });
            }
          }
        } else {
          if (code == MAINTENANCETIPCODE) {
            this.show = false;
            router.push("/system/maintenance");
          } else if (code == 1) {
            this.$dialog({
              title: "Oops",
              message: msg,
              confirmText: "Done",
              showCancelButton: false,
              onConfirm: async () => {
                this.show = false;
                router.push("/account/bet-order");
              },
            });
          } else if (code == 103035 || code == 103037) {
            let errstr: string = "";
            if (code == 103035) {
              //充值限额
              errstr = "Transaction limit exceeded.";
            }
            if (code == 103037) {
              //钱包余额不足
              let payType: string = "";
              if (
                params["identifier"] == PAY_METHOD.MAYA_PAY ||
                params["identifier"] == PAY_METHOD.MAYA_WEB
              ) {
                payType = "Maya";
              } else if (params["identifier"] == PAY_METHOD.GCASH_WEB) {
                payType = "GCash";
              }
              errstr = `Your ${payType} account has insufficient balance.`;
            }
            this.$dialog({
              title: "Oops",
              message: `${errstr}`,
              confirmText: "Done",
              showCancelButton: false,
              onConfirm: async () => {},
            });
          } else if (code == 103099) {
            this.$dialog({
              title: "Tips",
              message: "The payment was unsuccessful due to an abnormality.Please try again later.",
              confirmText: "Done",
              showCancelButton: false,
              onConfirm: async () => {},
            });
          } else {
            this.$dialog({
              title: "Tips",
              message: "The payment was unsuccessful due to an abnormality.Please try again later.",
              confirmText: "Done",
              showCancelButton: false,
              onConfirm: async () => {},
            });
          }
        }
      } catch (error) {
        this.stopDepTimeoutSchedule();

        console.error("Payment submission failed:", error);
        this.$dialog({
          title: "Tips",
          message: "Network error occurred. Please check your connection and try again.",
          confirmText: "Done",
          showCancelButton: false,
          onConfirm: () => {
            this.show = false;
          },
        });
      }
    },
    // 重置输入
    resetData() {
      this.selectedAmount = "";
      this.errTip = "";
      this.awardNum = 0;
      this.totalAmount = 0;
    },
  },
});

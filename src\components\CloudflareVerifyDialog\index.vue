<template>
  <van-overlay
    v-model:show="visible"
    :close-on-click-overlay="false"
    :close-on-popstate="false"
    :show-cancel-button="false"
    :show-confirm-button="false"
    class="cloudflare-verify-dialog"
  >
    <div class="dialog-content">
      <!-- Turnstile 容器 -->
      <div :id="containerId" class="turnstile-container" :class="{ loading: isLoading }">
        <div v-if="isLoading" class="loading-spinner">
          <van-loading type="spinner" size="24px" />
        </div>
      </div>
    </div>
  </van-overlay>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from "vue";
import {
  CloudflareMgr,
  CF_TURNSTILE_TYPE,
  type TurnstileResult,
  type VerifyCallback,
} from "@/utils/CloudflareMgr";

interface Props {
  /** 控制弹窗显示/隐藏 */
  modelValue: boolean;
  /** 验证类型 */
  cfType: CF_TURNSTILE_TYPE;
  /** 自定义 Site Key */
  siteKey?: string;
  /** 主题 */
  theme?: "light" | "dark" | "auto";
  /** 尺寸 */
  size?: "normal" | "compact";
  /** 外观 */
  appearance?: "always" | "execute" | "interaction-only";
  /** 验证成功后自动关闭延迟时间(ms) */
  autoCloseDelay?: number;
}

const props = withDefaults(defineProps<Props>(), {
  theme: "light",
  size: "normal",
  appearance: "always",
  autoCloseDelay: 5000,
});

const emit = defineEmits<{
  /** 更新v-model绑定值 */
  (e: "update:modelValue", value: boolean): void;
  /** 验证成功事件 */
  (e: "success", result: TurnstileResult): void;
  /** 验证失败事件 */
  (e: "error", error: string): void;
  /** 取消事件 */
  (e: "cancel"): void;
}>();

// 响应式状态
const visible = ref(props.modelValue);
const isLoading = ref(false);
const isSuccess = ref(false);
const errorMessage = ref("");
const widgetId = ref<string | null>(null);

// 生成唯一的容器ID
const containerId = computed(
  () => `turnstile-container-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`
);

// 监听modelValue变化
watch(
  () => props.modelValue,
  (val) => {
    visible.value = val;
    if (val) {
      // 防止重复初始化
      if (!isLoading.value && !widgetId.value) {
        initVerification();
      }
    } else {
      cleanup();
    }
  }
);

// 监听visible变化，同步到父组件
watch(visible, (val) => {
  if (val !== props.modelValue) {
    emit("update:modelValue", val);
  }
});

/**
 * 初始化验证
 */
const initVerification = async () => {
  // 防止重复初始化
  if (isLoading.value) {
    return;
  }

  if (widgetId.value) {
    cleanup();
  }

  // 重置状态
  isLoading.value = true;
  isSuccess.value = false;
  errorMessage.value = "";

  try {
    // 等待下一个tick，确保DOM已渲染
    await new Promise((resolve) => setTimeout(resolve, 100));

    // 渲染Turnstile组件
    const mgr = CloudflareMgr.instance;

    // 构建配置对象，只有当 siteKey 不为 undefined 时才传递
    const config: any = {
      theme: props.theme,
      size: props.size,
      appearance: props.appearance,
    };

    // 只有当 props.siteKey 有值时才添加到配置中
    if (props.siteKey) {
      config.siteKey = props.siteKey;
    }

    widgetId.value = await mgr.renderTurnstile(
      containerId.value,
      props.cfType,
      handleVerificationResult,
      config
    );

    isLoading.value = false;
  } catch (error) {
    isLoading.value = false;
    errorMessage.value = error instanceof Error ? error.message : "Failed to load verification";
    emit("error", errorMessage.value);
  }
};

/**
 * 处理验证结果
 */
const handleVerificationResult: VerifyCallback = async (result) => {
  if (result === false) {
    errorMessage.value = "Verification failed";
    emit("error", errorMessage.value);
    return;
  }

  if (result.success) {
    isSuccess.value = true;
    errorMessage.value = "";
    emit("success", result);

    // 自动关闭弹窗
    if (props.autoCloseDelay > 0) {
      setTimeout(() => {
        handleClose();
      }, props.autoCloseDelay);
    }
  } else {
    // 使用 CloudflareMgr 中的错误处理
    const { CFErrorHandler } = await import("@/utils/CloudflareMgr");

    // 解析错误信息
    const errorCode = result.errorCode || result.error || "Unknown error";
    const errorInfo = CFErrorHandler.parseError(errorCode);

    errorMessage.value = errorInfo.userFriendlyMessage;

    // 发出简单的错误消息，保持向后兼容
    emit("error", errorMessage.value);
  }
};

/**
 * 重试验证
 */
const retry = () => {
  cleanup();
  initVerification();
};

/**
 * 取消验证
 */
const handleCancel = () => {
  emit("cancel");
  handleClose();
};

/**
 * 关闭弹窗
 */
const handleClose = () => {
  visible.value = false;
  emit("update:modelValue", false);
};

/**
 * 清理资源
 */
const cleanup = () => {
  if (widgetId.value) {
    try {
      CloudflareMgr.instance.remove(widgetId.value);
    } catch (error) {
      // Silent cleanup
    }
    widgetId.value = null;
  }

  isLoading.value = false;
  isSuccess.value = false;
  errorMessage.value = "";
};

// 组件挂载时初始化
onMounted(() => {
  if (props.modelValue) {
    // 防止与 watch 重复初始化，添加延迟
    setTimeout(() => {
      if (!isLoading.value && !widgetId.value && props.modelValue) {
        initVerification();
      }
    }, 50);
  }
});

// 组件卸载时清理
onUnmounted(() => {
  cleanup();
});
</script>

<style scoped lang="scss">
.cloudflare-verify-dialog {
  z-index: 99999999999;
  .dialog-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    height: 100dvh;
  }
  .turnstile-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 80px;
    width: 100%;

    &.loading {
      background: transparent;
    }
  }
}

.loading-spinner {
  display: flex;
  align-items: center;
  color: #969799;
}
</style>
